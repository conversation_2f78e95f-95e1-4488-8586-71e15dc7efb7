import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

export default function HeroSection() {
  return (
    <section className="relative px-6 pt-12 pb-24">
      {/* Gradient blob */}
      <div
        className="absolute right-0 top-0 h-[300px] w-[300px] animate-pulse rounded-full bg-gradient-to-br from-[#5d8c62] via-[#d9b38c] to-[#f8f8f8] opacity-70 blur-3xl"
        aria-hidden="true"
      />

      <div className="relative">
        <h1 className="max-w-3xl text-6xl font-light leading-tight tracking-tight">
          FENCING THE
          <br />
          MACEDON RANGES
        </h1>
        <p className="text-xl md:text-2xl mb-8 max-w-2xl">
          Custom, high quality rural fencing & gates for homes and properties
        </p>

        <div className="mt-8 flex flex-col md:flex-row md:justify-between">
          <div className="max-w-md">
            <Link href="/quote">
              <Button className="bg-[#8bc283] hover:bg-[#7ab176] text-white rounded-md px-8 py-3">GET A QUOTE</Button>
            </Link>
          </div>
        </div>

        <p className="mt-12 max-w-xl text-sm leading-relaxed text-gray-600">
          We specialize in crafting beautiful, durable timber gates and fences that enhance the natural beauty of your
          property. From traditional post and rail to custom designs, we deliver unparalleled craftsmanship and
          professional service.
        </p>
      </div>
    </section>
  )
}
