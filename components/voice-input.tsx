"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2 } from "lucide-react"

interface VoiceInputProps {
  onTranscriptionComplete: (text: string) => void
  fieldName: string
  className?: string
}

export default function VoiceInput({ onTranscriptionComplete, fieldName, className = "" }: VoiceInputProps) {
  const [isRecording, setIsRecording] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])

  const startRecording = async () => {
    setError(null)
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder
      audioChunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      mediaRecorder.onstop = async () => {
        setIsProcessing(true)
        const audioBlob = new Blob(audioChunksRef.current, { type: "audio/webm" })

        // Create a FormData object to send the audio file
        const formData = new FormData()
        formData.append("audio", audioBlob, "recording.webm")
        formData.append("fieldName", fieldName)

        try {
          const response = await fetch("/api/transcribe", {
            method: "POST",
            body: formData,
          })

          if (!response.ok) {
            throw new Error(`Transcription failed: ${response.statusText}`)
          }

          const data = await response.json()
          onTranscriptionComplete(data.text)
        } catch (err) {
          console.error("Error during transcription:", err)
          setError(err instanceof Error ? err.message : "Failed to transcribe audio")
        } finally {
          setIsProcessing(false)

          // Stop all tracks in the stream to release the microphone
          stream.getTracks().forEach((track) => track.stop())
        }
      }

      mediaRecorder.start()
      setIsRecording(true)
    } catch (err) {
      console.error("Error accessing microphone:", err)
      setError("Could not access microphone. Please check permissions.")
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
    }
  }

  return (
    <div className={`inline-flex ${className}`}>
      {isProcessing ? (
        <Button type="button" size="icon" variant="outline" disabled className="relative">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="sr-only">Processing audio...</span>
        </Button>
      ) : isRecording ? (
        <Button type="button" size="icon" variant="destructive" onClick={stopRecording} className="relative">
          <MicOff className="h-4 w-4" />
          <span className="absolute -top-1 -right-1 flex h-3 w-3">
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
            <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
          </span>
          <span className="sr-only">Stop recording</span>
        </Button>
      ) : (
        <Button type="button" size="icon" variant="outline" onClick={startRecording} className="relative">
          <Mic className="h-4 w-4" />
          <span className="sr-only">Start voice input</span>
        </Button>
      )}

      {error && <div className="text-xs text-red-500 mt-1">{error}</div>}
    </div>
  )
}
