"use client";

import { useState } from "react";
import { cn } from "@/lib/utils";
import StepOneForm from "./step-one-form";
import StepTwoMap from "./step-two-map";
import { useToast } from "@/components/ui/use-toast";

interface QuoteWizardData {
    stepOne: {
        name: string;
        email: string;
        phone: string;
        propertyAddress: string;
        datePlanned: string;
    };
    stepTwo: {
        fences: Array<{ points: Array<[number, number]>; length: number }>;
        gates: Array<{ position: [number, number]; width: number }>;
    };
}

interface QuoteWizardProps extends React.HTMLAttributes<HTMLDivElement> {
    onComplete: (data: QuoteWizardData) => void;
    gateWidth?: number;
}

export default function QuoteWizard({
    onComplete,
    gateWidth = 3.6,
    className,
    ...props
}: QuoteWizardProps) {
    const { toast } = useToast();
    const [currentStep, setCurrentStep] = useState(1);
    const [wizardData, setWizardData] = useState<QuoteWizardData>({
        stepOne: {
            name: "",
            email: "",
            phone: "",
            propertyAddress: "",
            datePlanned: "",
        },
        stepTwo: {
            fences: [],
            gates: [],
        },
    });

    const handleStepOneChange = (data: QuoteWizardData["stepOne"]) => {
        setWizardData((prev) => ({
            ...prev,
            stepOne: data,
        }));
    };

    const handleStepTwoChange = (data: QuoteWizardData["stepTwo"]) => {
        setWizardData((prev) => ({
            ...prev,
            stepTwo: data,
        }));
    };

    const handleStepOneNext = () => {
        setCurrentStep(2);
        toast({
            title: "Step 1 Complete",
            description: "Now let's design your fence layout on the map",
            duration: 3000,
        });
    };

    const handleStepTwoNext = () => {
        // Complete the wizard
        onComplete(wizardData);
        toast({
            title: "Design Complete",
            description: "Your fence layout has been saved. Proceeding to options...",
            duration: 3000,
        });
    };

    const handleStepTwoBack = () => {
        setCurrentStep(1);
    };

    const handleVoiceInput = (fields: Record<string, string>) => {
        const fieldNames = Object.keys(fields);
        if (fieldNames.length > 0) {
            toast({
                title: "Voice Input Recognized",
                description: `Updated: ${fieldNames.join(", ")}`,
                duration: 3000,
            });
        }
    };

    return (
        <div
            className={cn(
                "w-full min-h-screen bg-gradient-to-br from-zinc-50 to-zinc-100 dark:from-zinc-900 dark:to-zinc-800",
                "py-8 px-4",
                className
            )}
            {...props}
        >
            {/* Progress Indicator */}
            <div className="max-w-4xl mx-auto mb-8">
                <div className="flex items-center justify-between">
                    <div className={`flex flex-col items-center ${currentStep >= 1 ? "text-[#5d8c62]" : "text-zinc-400"}`}>
                        <div
                            className={cn(
                                "h-12 w-12 rounded-full flex items-center justify-center border-2 font-semibold text-lg",
                                currentStep >= 1 
                                    ? "border-[#5d8c62] bg-[#5d8c62] text-white" 
                                    : "border-zinc-300 bg-white text-zinc-400"
                            )}
                        >
                            1
                        </div>
                        <span className="text-sm mt-2 font-medium">Contact & Address</span>
                    </div>
                    
                    <div className={cn(
                        "h-1 flex-1 mx-6 rounded-full transition-all duration-300",
                        currentStep >= 2 ? "bg-[#5d8c62]" : "bg-zinc-300"
                    )}></div>
                    
                    <div className={`flex flex-col items-center ${currentStep >= 2 ? "text-[#5d8c62]" : "text-zinc-400"}`}>
                        <div
                            className={cn(
                                "h-12 w-12 rounded-full flex items-center justify-center border-2 font-semibold text-lg",
                                currentStep >= 2 
                                    ? "border-[#5d8c62] bg-[#5d8c62] text-white" 
                                    : "border-zinc-300 bg-white text-zinc-400"
                            )}
                        >
                            2
                        </div>
                        <span className="text-sm mt-2 font-medium">Map Design</span>
                    </div>
                    
                    <div className={cn(
                        "h-1 flex-1 mx-6 rounded-full transition-all duration-300",
                        currentStep >= 3 ? "bg-[#5d8c62]" : "bg-zinc-300"
                    )}></div>
                    
                    <div className={`flex flex-col items-center ${currentStep >= 3 ? "text-[#5d8c62]" : "text-zinc-400"}`}>
                        <div
                            className={cn(
                                "h-12 w-12 rounded-full flex items-center justify-center border-2 font-semibold text-lg",
                                currentStep >= 3 
                                    ? "border-[#5d8c62] bg-[#5d8c62] text-white" 
                                    : "border-zinc-300 bg-white text-zinc-400"
                            )}
                        >
                            3
                        </div>
                        <span className="text-sm mt-2 font-medium">Options & Quote</span>
                    </div>
                </div>
            </div>

            {/* Step Content */}
            <div className="transition-all duration-500 ease-in-out">
                {currentStep === 1 && (
                    <StepOneForm
                        data={wizardData.stepOne}
                        onDataChange={handleStepOneChange}
                        onNext={handleStepOneNext}
                        onVoiceInput={handleVoiceInput}
                    />
                )}

                {currentStep === 2 && (
                    <StepTwoMap
                        propertyAddress={wizardData.stepOne.propertyAddress}
                        data={wizardData.stepTwo}
                        onDataChange={handleStepTwoChange}
                        onNext={handleStepTwoNext}
                        onBack={handleStepTwoBack}
                        gateWidth={gateWidth}
                    />
                )}
            </div>

            {/* Debug Info (remove in production) */}
            {process.env.NODE_ENV === "development" && (
                <div className="max-w-4xl mx-auto mt-8 p-4 bg-zinc-100 dark:bg-zinc-800 rounded-lg">
                    <h3 className="text-sm font-medium mb-2">Debug Info:</h3>
                    <pre className="text-xs text-zinc-600 dark:text-zinc-400 overflow-auto">
                        {JSON.stringify(wizardData, null, 2)}
                    </pre>
                </div>
            )}
        </div>
    );
}
