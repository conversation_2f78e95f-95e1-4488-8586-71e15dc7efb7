"use client";

import { cn } from "@/lib/utils";
import {
    User,
    AtSign,
    Phone,
    MapPin,
    Mic,
    Check,
    Calendar,
} from "lucide-react";
import { useState, useEffect } from "react";
import AddressAutocomplete from "./address-autocomplete";
import { But<PERSON> } from "@/components/ui/button";

interface StepOneFormData {
    name: string;
    email: string;
    phone: string;
    propertyAddress: string;
    datePlanned: string;
}

interface StepOneFormProps extends React.HTMLAttributes<HTMLDivElement> {
    data?: StepOneFormData;
    onDataChange: (data: StepOneFormData) => void;
    onNext: () => void;
    onVoiceInput: (fields: Record<string, string>) => void;
}

export default function StepOneForm({
    data = {
        name: "",
        email: "",
        phone: "",
        propertyAddress: "",
        datePlanned: "",
    },
    onDataChange,
    onNext,
    onVoiceInput,
    className,
    ...props
}: StepOneFormProps) {
    const [formData, setFormData] = useState<StepOneFormData>(data);
    const [isRecording, setIsRecording] = useState(false);
    const [recordingTime, setRecordingTime] = useState(0);
    const [isClient, setIsClient] = useState(false);
    const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
    const [audioChunks, setAudioChunks] = useState<Blob[]>([]);

    useEffect(() => {
        setIsClient(true);
    }, []);

    useEffect(() => {
        let intervalId: NodeJS.Timeout;

        if (isRecording) {
            intervalId = setInterval(() => {
                setRecordingTime((t) => t + 1);
            }, 1000);
        } else {
            setRecordingTime(0);
        }

        return () => clearInterval(intervalId);
    }, [isRecording]);

    const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, "0")}:${secs
            .toString()
            .padStart(2, "0")}`;
    };

    const handleInputChange = (field: keyof StepOneFormData, value: string) => {
        const updatedData = { ...formData, [field]: value };
        setFormData(updatedData);
        onDataChange(updatedData);
    };

    const handleVoiceClick = async () => {
        if (isRecording) {
            // Stop recording
            if (mediaRecorder) {
                mediaRecorder.stop();
            }
        } else {
            // Start recording
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                const recorder = new MediaRecorder(stream);
                setMediaRecorder(recorder);
                setAudioChunks([]);

                recorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        setAudioChunks((prev) => [...prev, event.data]);
                    }
                };

                recorder.onstop = async () => {
                    const audioBlob = new Blob(audioChunks, { type: "audio/webm" });
                    
                    // Send to transcription API
                    const formData = new FormData();
                    formData.append("audio", audioBlob, "recording.webm");

                    try {
                        const response = await fetch("/api/transcribe-form", {
                            method: "POST",
                            body: formData,
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.fields && Object.keys(data.fields).length > 0) {
                                // Update form data with voice input
                                const updatedData = { ...formData, ...data.fields };
                                setFormData(updatedData);
                                onDataChange(updatedData);
                                onVoiceInput(data.fields);
                            }
                        }
                    } catch (error) {
                        console.error("Voice transcription error:", error);
                    }

                    // Stop all tracks
                    stream.getTracks().forEach((track) => track.stop());
                    setIsRecording(false);
                };

                recorder.start();
                setIsRecording(true);
            } catch (error) {
                console.error("Error accessing microphone:", error);
            }
        }
    };

    const isFormValid = () => {
        return formData.name.trim() && 
               formData.email.trim() && 
               formData.phone.trim() && 
               formData.propertyAddress.trim();
    };

    return (
        <div
            className={cn(
                "w-full max-w-2xl mx-auto",
                "bg-white dark:bg-zinc-900",
                "border border-zinc-200 dark:border-zinc-800",
                "rounded-2xl overflow-hidden",
                "shadow-xl shadow-black/5",
                className
            )}
            {...props}
        >
            {/* Header */}
            <div className="px-8 pt-8 pb-6">
                <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold text-zinc-900 dark:text-white mb-2">
                        Property & Contact Details
                    </h2>
                    <p className="text-zinc-600 dark:text-zinc-400">
                        Tell us about your fencing project and where it will be located
                    </p>
                </div>

                {/* Voice Input Section */}
                <div className="flex flex-col items-center gap-4 mb-8 p-6 bg-zinc-50 dark:bg-zinc-800/50 rounded-xl">
                    <button
                        className={cn(
                            "group w-16 h-16 rounded-xl flex items-center justify-center transition-all duration-300",
                            isRecording
                                ? "bg-red-500 hover:bg-red-600 shadow-lg"
                                : "bg-[#5d8c62] hover:bg-[#4d7352] shadow-md hover:shadow-lg"
                        )}
                        type="button"
                        onClick={handleVoiceClick}
                    >
                        {isRecording ? (
                            <div
                                className="w-6 h-6 rounded-sm bg-white animate-pulse"
                                style={{ animationDuration: "1s" }}
                            />
                        ) : (
                            <Mic className="w-6 h-6 text-white" />
                        )}
                    </button>

                    <span
                        className={cn(
                            "font-mono text-sm transition-opacity duration-300",
                            isRecording
                                ? "text-red-600 dark:text-red-400"
                                : "text-zinc-500 dark:text-zinc-400"
                        )}
                    >
                        {formatTime(recordingTime)}
                    </span>

                    <div className="h-4 w-64 flex items-center justify-center gap-0.5">
                        {[...Array(32)].map((_, i) => (
                            <div
                                key={i}
                                className={cn(
                                    "w-0.5 rounded-full transition-all duration-300",
                                    isRecording
                                        ? "bg-red-500/70 dark:bg-red-400/70 animate-pulse"
                                        : "bg-zinc-300 dark:bg-zinc-600 h-1"
                                )}
                                style={
                                    isRecording && isClient
                                        ? {
                                              height: `${20 + Math.random() * 60}%`,
                                              animationDelay: `${i * 0.05}s`,
                                          }
                                        : undefined
                                }
                            />
                        ))}
                    </div>

                    <p className="text-sm text-zinc-600 dark:text-zinc-400 text-center">
                        {isRecording 
                            ? "Listening... Speak your name, email, phone, and property address" 
                            : "Click to speak your details or fill the form below"
                        }
                    </p>
                </div>
            </div>

            {/* Form Content */}
            <div className="px-8 pb-8 space-y-6">
                {/* Name and Email Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                        <label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            Full Name *
                        </label>
                        <div className="relative">
                            <User className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-zinc-400" />
                            <input
                                type="text"
                                value={formData.name}
                                onChange={(e) => handleInputChange("name", e.target.value)}
                                placeholder="John Smith"
                                className="w-full h-12 pl-10 pr-4 rounded-lg
                                    bg-zinc-50 dark:bg-zinc-800/50
                                    text-zinc-900 dark:text-zinc-100
                                    border border-zinc-200 dark:border-zinc-700
                                    focus:outline-none focus:ring-2 focus:ring-[#5d8c62]/20 focus:border-[#5d8c62]
                                    transition-all"
                            />
                        </div>
                    </div>

                    <div className="space-y-2">
                        <label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            Email Address *
                        </label>
                        <div className="relative">
                            <AtSign className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-zinc-400" />
                            <input
                                type="email"
                                value={formData.email}
                                onChange={(e) => handleInputChange("email", e.target.value)}
                                placeholder="<EMAIL>"
                                className="w-full h-12 pl-10 pr-4 rounded-lg
                                    bg-zinc-50 dark:bg-zinc-800/50
                                    text-zinc-900 dark:text-zinc-100
                                    border border-zinc-200 dark:border-zinc-700
                                    focus:outline-none focus:ring-2 focus:ring-[#5d8c62]/20 focus:border-[#5d8c62]
                                    transition-all"
                            />
                        </div>
                    </div>
                </div>

                {/* Phone and Date Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                        <label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            Phone Number *
                        </label>
                        <div className="relative">
                            <Phone className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-zinc-400" />
                            <input
                                type="tel"
                                value={formData.phone}
                                onChange={(e) => handleInputChange("phone", e.target.value)}
                                placeholder="0412 345 678"
                                className="w-full h-12 pl-10 pr-4 rounded-lg
                                    bg-zinc-50 dark:bg-zinc-800/50
                                    text-zinc-900 dark:text-zinc-100
                                    border border-zinc-200 dark:border-zinc-700
                                    focus:outline-none focus:ring-2 focus:ring-[#5d8c62]/20 focus:border-[#5d8c62]
                                    transition-all"
                            />
                        </div>
                    </div>

                    <div className="space-y-2">
                        <label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            Planned Date
                        </label>
                        <div className="relative">
                            <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-zinc-400" />
                            <input
                                type="date"
                                value={formData.datePlanned}
                                onChange={(e) => handleInputChange("datePlanned", e.target.value)}
                                className="w-full h-12 pl-10 pr-4 rounded-lg
                                    bg-zinc-50 dark:bg-zinc-800/50
                                    text-zinc-900 dark:text-zinc-100
                                    border border-zinc-200 dark:border-zinc-700
                                    focus:outline-none focus:ring-2 focus:ring-[#5d8c62]/20 focus:border-[#5d8c62]
                                    transition-all"
                            />
                        </div>
                    </div>
                </div>

                {/* Property Address */}
                <div className="space-y-2">
                    <label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                        Property Address *
                    </label>
                    <div className="relative">
                        <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-zinc-400 z-10" />
                        <AddressAutocomplete
                            id="propertyAddress"
                            name="propertyAddress"
                            placeholder="123 Farm Road, Kyneton VIC"
                            value={formData.propertyAddress}
                            onChange={(value) => handleInputChange("propertyAddress", value)}
                            className="pl-10 h-12 rounded-lg
                                bg-zinc-50 dark:bg-zinc-800/50
                                text-zinc-900 dark:text-zinc-100
                                border border-zinc-200 dark:border-zinc-700
                                focus:outline-none focus:ring-2 focus:ring-[#5d8c62]/20 focus:border-[#5d8c62]
                                transition-all"
                        />
                    </div>
                    <p className="text-xs text-zinc-500">
                        Where the fencing work will be done
                    </p>
                </div>

                {/* Next Button */}
                <div className="pt-6">
                    <Button
                        onClick={onNext}
                        disabled={!isFormValid()}
                        className={cn(
                            "w-full h-12 rounded-lg font-medium transition-all",
                            isFormValid()
                                ? "bg-[#5d8c62] hover:bg-[#4d7352] text-white shadow-md hover:shadow-lg"
                                : "bg-zinc-200 dark:bg-zinc-700 text-zinc-400 dark:text-zinc-500 cursor-not-allowed"
                        )}
                    >
                        {isFormValid() ? (
                            <>
                                <Check className="w-4 h-4 mr-2" />
                                Continue to Map Design
                            </>
                        ) : (
                            "Please fill in all required fields"
                        )}
                    </Button>
                </div>
            </div>
        </div>
    );
}
