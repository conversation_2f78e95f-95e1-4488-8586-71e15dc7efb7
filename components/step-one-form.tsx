"use client";

import { cn } from "@/lib/utils";
import {
    User,
    AtSign,
    Phone,
    MapPin,
    Check,
    Calendar,
} from "lucide-react";
import { useState, useEffect } from "react";
import AddressAutocomplete from "./address-autocomplete";
import { But<PERSON> } from "@/components/ui/button";
import ConversationalVoiceAssistant from "./conversational-voice-assistant";

interface StepOneFormData {
    name: string;
    email: string;
    phone: string;
    propertyAddress: string;
    datePlanned: string;
}

interface StepOneFormProps extends React.HTMLAttributes<HTMLDivElement> {
    data?: StepOneFormData;
    onDataChange: (data: StepOneFormData) => void;
    onNext: () => void;
    onVoiceInput: (fields: Record<string, string>) => void;
}

export default function StepOneForm({
    data = {
        name: "",
        email: "",
        phone: "",
        propertyAddress: "",
        datePlanned: "",
    },
    onDataChange,
    onNext,
    onVoiceInput,
    className,
    ...props
}: StepOneFormProps) {
    const [formData, setFormData] = useState<StepOneFormData>(data);
    const [conversationMessages, setConversationMessages] = useState<any[]>([]);
    const [isConversationActive, setIsConversationActive] = useState(false);

    // Agent ID for the fencing quote assistant
    const AGENT_ID = "agent_01jx9e9wnsejt8z0tae0z7wf89";

    // Handle field updates from conversational AI
    const handleFieldUpdate = (field: string, value: string) => {
        const fieldMap: Record<string, keyof StepOneFormData> = {
            name: "name",
            email: "email",
            phone: "phone",
            address: "propertyAddress",
            propertyAddress: "propertyAddress",
            date: "datePlanned",
            datePlanned: "datePlanned"
        };

        const mappedField = fieldMap[field];
        if (mappedField) {
            const updatedData = { ...formData, [mappedField]: value };
            setFormData(updatedData);
            onDataChange(updatedData);

            // Notify parent of voice input
            onVoiceInput({ [field]: value });
        }
    };

    // Handle conversation updates
    const handleConversationUpdate = (messages: any[]) => {
        setConversationMessages(messages);
    };

    // Handle step completion from AI
    const handleStepComplete = () => {
        if (isFormValid()) {
            onNext();
        }
    };

    const handleInputChange = (field: keyof StepOneFormData, value: string) => {
        const updatedData = { ...formData, [field]: value };
        setFormData(updatedData);
        onDataChange(updatedData);
    };



    const isFormValid = () => {
        return formData.name.trim() &&
               formData.email.trim() &&
               formData.phone.trim() &&
               formData.propertyAddress.trim();
    };

    return (
        <div
            className={cn(
                "w-full max-w-2xl mx-auto",
                "bg-white dark:bg-zinc-900",
                "border border-zinc-200 dark:border-zinc-800",
                "rounded-2xl overflow-hidden",
                "shadow-xl shadow-black/5",
                className
            )}
            {...props}
        >
            {/* Header */}
            <div className="px-8 pt-8 pb-6">
                <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold text-zinc-900 dark:text-white mb-2">
                        Property & Contact Details
                    </h2>
                    <p className="text-zinc-600 dark:text-zinc-400">
                        Tell us about your fencing project and where it will be located
                    </p>
                </div>

                {/* Conversational Voice Assistant */}
                <div className="flex flex-col items-center gap-4 mb-8 p-6 bg-zinc-50 dark:bg-zinc-800/50 rounded-xl">
                    <h3 className="text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2">
                        Voice Assistant
                    </h3>
                    <p className="text-sm text-zinc-600 dark:text-zinc-400 text-center mb-4">
                        Have a conversation with our AI assistant to fill in your details naturally
                    </p>

                    <ConversationalVoiceAssistant
                        agentId={AGENT_ID}
                        onFieldUpdate={handleFieldUpdate}
                        onStepComplete={handleStepComplete}
                        onConversationUpdate={handleConversationUpdate}
                        currentStep="contact"
                        className="w-full"
                    />
                </div>
            </div>

            {/* Form Content */}
            <div className="px-8 pb-8 space-y-6">
                {/* Name and Email Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                        <label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            Full Name *
                        </label>
                        <div className="relative">
                            <User className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-zinc-400" />
                            <input
                                type="text"
                                value={formData.name}
                                onChange={(e) => handleInputChange("name", e.target.value)}
                                placeholder="John Smith"
                                className="w-full h-12 pl-10 pr-4 rounded-lg
                                    bg-zinc-50 dark:bg-zinc-800/50
                                    text-zinc-900 dark:text-zinc-100
                                    border border-zinc-200 dark:border-zinc-700
                                    focus:outline-none focus:ring-2 focus:ring-[#5d8c62]/20 focus:border-[#5d8c62]
                                    transition-all"
                            />
                        </div>
                    </div>

                    <div className="space-y-2">
                        <label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            Email Address *
                        </label>
                        <div className="relative">
                            <AtSign className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-zinc-400" />
                            <input
                                type="email"
                                value={formData.email}
                                onChange={(e) => handleInputChange("email", e.target.value)}
                                placeholder="<EMAIL>"
                                className="w-full h-12 pl-10 pr-4 rounded-lg
                                    bg-zinc-50 dark:bg-zinc-800/50
                                    text-zinc-900 dark:text-zinc-100
                                    border border-zinc-200 dark:border-zinc-700
                                    focus:outline-none focus:ring-2 focus:ring-[#5d8c62]/20 focus:border-[#5d8c62]
                                    transition-all"
                            />
                        </div>
                    </div>
                </div>

                {/* Phone and Date Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                        <label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            Phone Number *
                        </label>
                        <div className="relative">
                            <Phone className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-zinc-400" />
                            <input
                                type="tel"
                                value={formData.phone}
                                onChange={(e) => handleInputChange("phone", e.target.value)}
                                placeholder="0412 345 678"
                                className="w-full h-12 pl-10 pr-4 rounded-lg
                                    bg-zinc-50 dark:bg-zinc-800/50
                                    text-zinc-900 dark:text-zinc-100
                                    border border-zinc-200 dark:border-zinc-700
                                    focus:outline-none focus:ring-2 focus:ring-[#5d8c62]/20 focus:border-[#5d8c62]
                                    transition-all"
                            />
                        </div>
                    </div>

                    <div className="space-y-2">
                        <label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            Planned Date
                        </label>
                        <div className="relative">
                            <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-zinc-400" />
                            <input
                                type="date"
                                value={formData.datePlanned}
                                onChange={(e) => handleInputChange("datePlanned", e.target.value)}
                                className="w-full h-12 pl-10 pr-4 rounded-lg
                                    bg-zinc-50 dark:bg-zinc-800/50
                                    text-zinc-900 dark:text-zinc-100
                                    border border-zinc-200 dark:border-zinc-700
                                    focus:outline-none focus:ring-2 focus:ring-[#5d8c62]/20 focus:border-[#5d8c62]
                                    transition-all"
                            />
                        </div>
                    </div>
                </div>

                {/* Property Address */}
                <div className="space-y-2">
                    <label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                        Property Address *
                    </label>
                    <div className="relative">
                        <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-zinc-400 z-10" />
                        <AddressAutocomplete
                            id="propertyAddress"
                            name="propertyAddress"
                            placeholder="123 Farm Road, Kyneton VIC"
                            value={formData.propertyAddress}
                            onChange={(value) => handleInputChange("propertyAddress", value)}
                            className="pl-10 h-12 rounded-lg
                                bg-zinc-50 dark:bg-zinc-800/50
                                text-zinc-900 dark:text-zinc-100
                                border border-zinc-200 dark:border-zinc-700
                                focus:outline-none focus:ring-2 focus:ring-[#5d8c62]/20 focus:border-[#5d8c62]
                                transition-all"
                        />
                    </div>
                    <p className="text-xs text-zinc-500">
                        Where the fencing work will be done
                    </p>
                </div>

                {/* Next Button */}
                <div className="pt-6">
                    <Button
                        onClick={onNext}
                        disabled={!isFormValid()}
                        className={cn(
                            "w-full h-12 rounded-lg font-medium transition-all",
                            isFormValid()
                                ? "bg-[#5d8c62] hover:bg-[#4d7352] text-white shadow-md hover:shadow-lg"
                                : "bg-zinc-200 dark:bg-zinc-700 text-zinc-400 dark:text-zinc-500 cursor-not-allowed"
                        )}
                    >
                        {isFormValid() ? (
                            <>
                                <Check className="w-4 h-4 mr-2" />
                                Continue to Map Design
                            </>
                        ) : (
                            "Please fill in all required fields"
                        )}
                    </Button>
                </div>
            </div>
        </div>
    );
}
