import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

export default function QuotePreview() {
  return (
    <section className="py-24 px-6 bg-[#3c2a1e] text-white">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-8 md:mb-0 md:mr-8">
            <h2 className="text-3xl font-light mb-6">GET AN INSTANT QUOTE</h2>
            <p className="max-w-xl text-gray-300">
              Our AI-powered quoting system can provide you with an instant estimate for your timber gates and fencing
              project. Simply answer a few questions about your requirements, and we'll generate a detailed quote for
              you.
            </p>
            <div className="mt-8">
              <Link href="/quote-wizard">
                <Button className="bg-[#5d8c62] hover:bg-[#4d7352] text-white rounded-full px-8 py-6">
                  TRY OUR AI QUOTE TOOL
                </Button>
              </Link>
            </div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 w-full md:w-auto">
            <div className="flex items-center mb-4">
              <div className="h-8 w-8 rounded-full bg-[#5d8c62] flex items-center justify-center mr-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-medium">AI Quote Tool Benefits</h3>
            </div>
            <ul className="space-y-3">
              <li className="flex items-start">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 mt-0.5 text-[#5d8c62]"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Instant pricing estimates</span>
              </li>
              <li className="flex items-start">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 mt-0.5 text-[#5d8c62]"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Customize your gate and fencing requirements</span>
              </li>
              <li className="flex items-start">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 mt-0.5 text-[#5d8c62]"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Explore different cypress timber options</span>
              </li>
              <li className="flex items-start">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 mt-0.5 text-[#5d8c62]"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Save and compare multiple quotes</span>
              </li>
              <li className="flex items-start">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 mt-0.5 text-[#5d8c62]"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Book a consultation with one click</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  )
}
