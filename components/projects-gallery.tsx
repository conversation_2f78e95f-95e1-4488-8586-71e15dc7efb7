"use client"

import Image from "next/image"
import { useState } from "react"
import { Button } from "@/components/ui/button"

export default function ProjectsGallery() {
  const [activeCategory, setActiveCategory] = useState("all")

  const projects = [
    {
      title: "Country Swing Gate",
      category: "gates",
      location: "Woodend",
      image: "/country-swing-timber-gate.png",
    },
    {
      title: "Decorative Gate",
      category: "gates",
      location: "Kyneton",
      image: "/decorative-timber-gate.png",
    },
    {
      title: "3-Rail Timber Fence",
      category: "rural",
      location: "Macedon",
      image: "/rural-timber-fence.png",
    },
    {
      title: "Post & Wire Fencing",
      category: "rural",
      location: "Gisborne",
      image: "/placeholder.svg?height=600&width=800&query=post and wire farm fencing",
    },
    {
      title: "Automatic Gate System",
      category: "gates",
      location: "Romsey",
      image: "/placeholder.svg?height=600&width=800&query=automatic timber gate system",
    },
    {
      title: "Custom Security Gate",
      category: "gates",
      location: "Riddells Creek",
      image: "/placeholder.svg?height=600&width=800&query=solid security timber gate",
    },
  ]

  const filteredProjects =
    activeCategory === "all" ? projects : projects.filter((project) => project.category === activeCategory)

  return (
    <section id="projects" className="py-24 px-6 bg-[#f8f8f8]">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start mb-12">
          <h2 className="text-3xl font-light">OUR PROJECTS</h2>
          <p className="max-w-xl mt-4 md:mt-0 text-gray-600">
            Browse our portfolio of completed timber fencing projects throughout the Macedon Ranges area.
          </p>
        </div>

        <div className="flex flex-wrap gap-4 mb-12">
          <Button
            variant={activeCategory === "all" ? "default" : "outline"}
            onClick={() => setActiveCategory("all")}
            className={activeCategory === "all" ? "bg-[#5d8c62] hover:bg-[#4d7352]" : ""}
          >
            All Projects
          </Button>
          <Button
            variant={activeCategory === "gates" ? "default" : "outline"}
            onClick={() => setActiveCategory("gates")}
            className={activeCategory === "gates" ? "bg-[#5d8c62] hover:bg-[#4d7352]" : ""}
          >
            Gates
          </Button>
          <Button
            variant={activeCategory === "rural" ? "default" : "outline"}
            onClick={() => setActiveCategory("rural")}
            className={activeCategory === "rural" ? "bg-[#5d8c62] hover:bg-[#4d7352]" : ""}
          >
            Fencing
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project, index) => (
            <div key={index} className="group relative overflow-hidden rounded-lg">
              <div className="relative h-80 w-full overflow-hidden">
                <Image
                  src={project.image || "/placeholder.svg"}
                  alt={project.title}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black bg-opacity-20 transition-opacity duration-300 group-hover:bg-opacity-40"></div>
              </div>
              <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                <h3 className="text-xl font-medium">{project.title}</h3>
                <p className="text-sm opacity-90">Location: {project.location}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Button variant="outline" className="rounded-full border-2 px-8">
            VIEW ALL PROJECTS
          </Button>
        </div>
      </div>
    </section>
  )
}
