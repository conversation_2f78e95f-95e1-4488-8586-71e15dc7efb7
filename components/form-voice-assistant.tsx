"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2 } from "lucide-react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

interface FormVoiceAssistantProps {
  onTranscriptionComplete: (fields: Record<string, string>) => void
}

export default function FormVoiceAssistant({ onTranscriptionComplete }: FormVoiceAssistantProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [transcript, setTranscript] = useState<string>("")
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])

  const startRecording = async () => {
    setError(null)
    setTranscript("")
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder
      audioChunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      mediaRecorder.onstop = async () => {
        setIsProcessing(true)
        const audioBlob = new Blob(audioChunksRef.current, { type: "audio/webm" })

        // Create a FormData object to send the audio file
        const formData = new FormData()
        formData.append("audio", audioBlob, "recording.webm")
        formData.append("fieldName", "form")

        try {
          const response = await fetch("/api/transcribe-form", {
            method: "POST",
            body: formData,
          })

          if (!response.ok) {
            throw new Error(`Transcription failed: ${response.statusText}`)
          }

          const data = await response.json()
          setTranscript(data.text)

          if (data.fields && Object.keys(data.fields).length > 0) {
            onTranscriptionComplete(data.fields)
          }
        } catch (err) {
          console.error("Error during transcription:", err)
          setError(err instanceof Error ? err.message : "Failed to transcribe audio")
        } finally {
          setIsProcessing(false)

          // Stop all tracks in the stream to release the microphone
          stream.getTracks().forEach((track) => track.stop())
        }
      }

      mediaRecorder.start()
      setIsRecording(true)
    } catch (err) {
      console.error("Error accessing microphone:", err)
      setError("Could not access microphone. Please check permissions.")
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
    }
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 bg-[#5d8c62] text-white hover:bg-[#4d7352]"
            onClick={() => setIsOpen(true)}
          >
            <Mic className="h-4 w-4" />
            <span>Voice Assistant</span>
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Voice Assistant</DialogTitle>
            <DialogDescription>
              Speak to fill in multiple form fields at once. For example, you can say your name, email address, and phone number in one go.
            </DialogDescription>
          </DialogHeader>

          <div className="flex flex-col items-center justify-center py-6">
            {isProcessing ? (
              <div className="flex flex-col items-center">
                <Loader2 className="h-12 w-12 animate-spin text-[#5d8c62]" />
                <p className="mt-4 text-center text-sm text-gray-500">Processing your voice input...</p>
              </div>
            ) : isRecording ? (
              <div className="flex flex-col items-center">
                <Button size="lg" variant="destructive" className="h-16 w-16 rounded-full" onClick={stopRecording}>
                  <MicOff className="h-8 w-8" />
                </Button>
                <p className="mt-4 text-center text-sm">Recording... Click to stop</p>
                <div className="mt-2 flex justify-center">
                  <span className="relative flex h-3 w-3">
                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                    <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                  </span>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <Button
                  size="lg"
                  className="h-16 w-16 rounded-full bg-[#5d8c62] hover:bg-[#4d7352]"
                  onClick={startRecording}
                >
                  <Mic className="h-8 w-8" />
                </Button>
                <p className="mt-4 text-center text-sm">Click to start speaking</p>
              </div>
            )}

            {error && <div className="mt-4 text-center text-sm text-red-500">{error}</div>}

            {transcript && (
              <div className="mt-6 w-full">
                <h4 className="mb-2 text-sm font-medium">Transcript:</h4>
                <div className="rounded-md bg-gray-50 p-3 text-sm">{transcript}</div>
              </div>
            )}
          </div>

          <div className="flex justify-end">
            <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isRecording || isProcessing}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
