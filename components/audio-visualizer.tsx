"use client"

import { useRef, useEffect } from "react"

interface AudioVisualizerProps {
  audioStream: MediaStream | null
  isActive: boolean
}

export default function AudioVisualizer({ audioStream, isActive }: AudioVisualizerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const analyserRef = useRef<AnalyserNode | null>(null)
  const dataArrayRef = useRef<Uint8Array | null>(null)

  useEffect(() => {
    if (!isActive || !audioStream || !canvasRef.current) {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      return
    }

    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
    const analyser = audioContext.createAnalyser()
    analyserRef.current = analyser
    analyser.fftSize = 256
    const bufferLength = analyser.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    dataArrayRef.current = dataArray

    const source = audioContext.createMediaStreamSource(audioStream)
    source.connect(analyser)

    const canvas = canvasRef.current
    const canvasCtx = canvas.getContext("2d")
    if (!canvasCtx) return

    const draw = () => {
      if (!isActive) return

      animationRef.current = requestAnimationFrame(draw)

      if (!analyserRef.current || !dataArrayRef.current || !canvasCtx) return

      analyserRef.current.getByteFrequencyData(dataArrayRef.current)

      const WIDTH = canvas.width
      const HEIGHT = canvas.height

      canvasCtx.clearRect(0, 0, WIDTH, HEIGHT)

      const barWidth = (WIDTH / bufferLength) * 2.5
      let barHeight
      let x = 0

      for (let i = 0; i < bufferLength; i++) {
        barHeight = dataArrayRef.current[i] / 2

        // Use a gradient from the brand color to a lighter shade
        const gradient = canvasCtx.createLinearGradient(0, HEIGHT, 0, HEIGHT - barHeight)
        gradient.addColorStop(0, "#5d8c62")
        gradient.addColorStop(1, "#8bc283")

        canvasCtx.fillStyle = gradient
        canvasCtx.fillRect(x, HEIGHT - barHeight, barWidth, barHeight)

        x += barWidth + 1
      }
    }

    draw()

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      if (audioContext.state !== "closed") {
        audioContext.close()
      }
    }
  }, [audioStream, isActive])

  return (
    <div className="w-full h-16 bg-gray-50 rounded-md overflow-hidden">
      <canvas ref={canvasRef} width={300} height={64} className="w-full h-full" />
    </div>
  )
}
