import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { HelpCircle } from "lucide-react"

export default function VoiceCommandHelp() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon">
          <HelpCircle className="h-5 w-5" />
          <span className="sr-only">Voice Command Help</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Voice Commands</DialogTitle>
          <DialogDescription>Available voice commands for navigating the quote form</DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Navigation Commands</h3>
            <ul className="text-sm space-y-1">
              <li className="flex justify-between">
                <span className="font-medium">"Next step", "Continue"</span>
                <span className="text-gray-500">Go to next step</span>
              </li>
              <li className="flex justify-between">
                <span className="font-medium">"Previous step", "Go back"</span>
                <span className="text-gray-500">Go to previous step</span>
              </li>
              <li className="flex justify-between">
                <span className="font-medium">"Go to step 1", "First step"</span>
                <span className="text-gray-500">Go to Your Details</span>
              </li>
              <li className="flex justify-between">
                <span className="font-medium">"Go to step 2", "Site details"</span>
                <span className="text-gray-500">Go to Site Details</span>
              </li>
              <li className="flex justify-between">
                <span className="font-medium">"Go to step 3", "Fence details"</span>
                <span className="text-gray-500">Go to Fence Details</span>
              </li>
              <li className="flex justify-between">
                <span className="font-medium">"Go to step 4", "Quote"</span>
                <span className="text-gray-500">Go to Your Quote</span>
              </li>
              <li className="flex justify-between">
                <span className="font-medium">"Submit", "Generate quote"</span>
                <span className="text-gray-500">Generate your quote</span>
              </li>
            </ul>
          </div>
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Tips for Using Voice Commands</h3>
            <ul className="text-sm list-disc pl-5 space-y-1">
              <li>Speak clearly and at a normal pace</li>
              <li>Wait for the command to be recognized before speaking again</li>
              <li>Use the exact phrases listed above for best results</li>
              <li>You can toggle voice commands on/off with the button in the header</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
