"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { ToastAction } from "@/components/ui/toast"

interface VoiceCommandListenerProps {
  onCommand: (command: string) => void
  isActive: boolean
  setIsActive: (active: boolean) => void
}

export default function VoiceCommandListener({ onCommand, isActive, setIsActive }: VoiceCommandListenerProps) {
  const [isListening, setIsListening] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [recognizedText, setRecognizedText] = useState<string>("")
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])
  const commandTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Start listening for commands when active
  useEffect(() => {
    if (isActive && !isListening && !isProcessing) {
      startListening()
    } else if (!isActive && isListening) {
      stopListening()
    }
  }, [isActive, isListening, isProcessing])

  // Announce when voice commands are toggled using GROQ API
  useEffect(() => {
    if (isActive) {
      // Use GROQ API for text-to-speech
      speakTextWithGroq("Voice commands activated. You can now navigate using your voice.")

      toast({
        title: "Voice Commands Activated",
        description:
          "You can now navigate the form using voice commands. Click the help icon to see available commands.",
        action: (
          <Button variant="outline" size="sm" onClick={() => setIsActive(false)}>
            Disable
          </Button>
        ),
      })
    } else {
      // Only speak if it was previously active (not on initial load)
      if (typeof window !== "undefined" && document.visibilityState === "visible") {
        speakTextWithGroq("Voice commands deactivated.")
      }
    }
  }, [isActive])

  // Function to speak text using Web Speech API
  const speakTextWithGroq = async (text: string) => {
    try {
      // Check if the browser supports speech synthesis
      if ('speechSynthesis' in window) {
        // Cancel any ongoing speech
        window.speechSynthesis.cancel()
        
        const utterance = new SpeechSynthesisUtterance(text)
        utterance.rate = 0.9
        utterance.pitch = 1
        utterance.volume = 0.7
        
        // Use a more natural voice if available
        const voices = window.speechSynthesis.getVoices()
        const preferredVoice = voices.find(voice => 
          voice.name.includes('Enhanced') || 
          voice.name.includes('Premium') ||
          voice.lang.startsWith('en-AU') ||
          voice.lang.startsWith('en-US')
        )
        
        if (preferredVoice) {
          utterance.voice = preferredVoice
        }
        
        window.speechSynthesis.speak(utterance)
      } else {
        console.warn("Speech synthesis not supported in this browser")
      }
    } catch (error) {
      console.error("Error speaking text:", error)
    }
  }

  const startListening = async () => {
    setError(null)
    setRecognizedText("")
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder
      audioChunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      mediaRecorder.onstop = async () => {
        setIsProcessing(true)
        const audioBlob = new Blob(audioChunksRef.current, { type: "audio/webm" })

        // Create a FormData object to send the audio file
        const formData = new FormData()
        formData.append("audio", audioBlob, "recording.webm")
        formData.append("type", "command")

        let data: any = null // Declare data variable here

        try {
          const response = await fetch("/api/transcribe-command", {
            method: "POST",
            body: formData,
          })

          if (!response.ok) {
            const errorText = await response.text()
            throw new Error(`Transcription failed: ${response.status} ${errorText}`)
          }

          data = await response.json()
          setRecognizedText(data.text)

          if (data.command) {
            // Show toast with recognized command
            toast({
              title: "Voice Command Recognized",
              description: `"${data.command}"`,
              action: <ToastAction altText="OK">OK</ToastAction>,
            })

            // Execute the command
            onCommand(data.command)

            // Provide audio feedback using GROQ
            speakTextWithGroq(`Command recognized: ${data.command}`)
          }
        } catch (err) {
          console.error("Error during command transcription:", err)
          setError(err instanceof Error ? err.message : "Failed to transcribe audio")

          // Restart listening after error
          commandTimeoutRef.current = setTimeout(() => {
            if (isActive) {
              startListening()
            }
          }, 3000)
        } finally {
          // If there was an error but we still want to try to continue
          if (!data || !data.command) {
            // Restart listening after a short delay even if there was an error
            commandTimeoutRef.current = setTimeout(() => {
              if (isActive) {
                startListening()
              }
            }, 3000)
          }
          setIsProcessing(false)

          // Stop all tracks in the stream to release the microphone
          stream.getTracks().forEach((track) => track.stop())
        }
      }

      // Set a timeout to stop recording after 5 seconds
      const recordingTimeout = setTimeout(() => {
        if (mediaRecorderRef.current && mediaRecorderRef.current.state === "recording") {
          mediaRecorderRef.current.stop()
        }
      }, 5000)

      mediaRecorder.start()
      setIsListening(true)

      return () => {
        clearTimeout(recordingTimeout)
      }
    } catch (err) {
      console.error("Error accessing microphone:", err)
      setError(err instanceof Error ? err.message : "Could not access microphone. Please check permissions.")
      setIsActive(false)
    }
  }

  const stopListening = () => {
    if (mediaRecorderRef.current && isListening) {
      mediaRecorderRef.current.stop()
      setIsListening(false)
    }

    if (commandTimeoutRef.current) {
      clearTimeout(commandTimeoutRef.current)
    }
  }

  const toggleListening = () => {
    setIsActive(!isActive)
  }

  return (
    <div className="relative">
      <Button
        type="button"
        size="sm"
        variant={isActive ? "default" : "outline"}
        onClick={toggleListening}
        className={`relative ${isActive ? "bg-[#5d8c62] hover:bg-[#4d7352]" : ""}`}
      >
        {isProcessing ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            <span>Processing...</span>
          </>
        ) : isListening ? (
          <>
            <MicOff className="h-4 w-4 mr-2" />
            <span>Voice Commands Active</span>
            <span className="absolute -top-1 -right-1 flex h-3 w-3">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
              <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
            </span>
          </>
        ) : (
          <>
            <Mic className="h-4 w-4 mr-2" />
            <span>Enable Voice Commands</span>
          </>
        )}
      </Button>

      {error && <div className="text-xs text-red-500 mt-1">{error}</div>}
    </div>
  )
}
