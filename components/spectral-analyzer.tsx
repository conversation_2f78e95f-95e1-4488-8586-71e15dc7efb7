"use client";

import { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";

interface SpectralAnalyzerProps {
    isActive: boolean;
    audioStream?: MediaStream;
    className?: string;
}

export default function SpectralAnalyzer({ isActive, audioStream, className }: SpectralAnalyzerProps) {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const animationRef = useRef<number>();
    const analyzerRef = useRef<AnalyserNode>();
    const audioContextRef = useRef<AudioContext>();
    const [isInitialized, setIsInitialized] = useState(false);
    const [audioLevel, setAudioLevel] = useState(0);

    useEffect(() => {
        if (!isActive || !audioStream) {
            cleanup();
            return;
        }

        initializeAnalyzer();
        return cleanup;
    }, [isActive, audioStream]);

    const initializeAnalyzer = async () => {
        try {
            console.log("🔍 [DEBUG] SpectralAnalyzer: Initializing audio analyzer...");
            
            if (!audioStream) {
                console.error("❌ [DEBUG] SpectralAnalyzer: No audio stream provided");
                return;
            }

            // Create audio context
            audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
            const audioContext = audioContextRef.current;

            // Create analyzer node
            analyzerRef.current = audioContext.createAnalyser();
            analyzerRef.current.fftSize = 256;
            analyzerRef.current.smoothingTimeConstant = 0.8;

            // Connect audio stream to analyzer
            const source = audioContext.createMediaStreamSource(audioStream);
            source.connect(analyzerRef.current);

            console.log("✅ [DEBUG] SpectralAnalyzer: Audio analyzer initialized");
            setIsInitialized(true);
            startVisualization();

        } catch (error) {
            console.error("❌ [DEBUG] SpectralAnalyzer: Failed to initialize:", error);
        }
    };

    const startVisualization = () => {
        if (!analyzerRef.current || !canvasRef.current) return;

        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        const analyzer = analyzerRef.current;
        const bufferLength = analyzer.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        const draw = () => {
            if (!isActive) return;

            analyzer.getByteFrequencyData(dataArray);

            // Calculate audio level for debugging
            const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;
            setAudioLevel(Math.round(average));

            // Clear canvas
            ctx.fillStyle = 'rgb(0, 0, 0)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw frequency bars
            const barWidth = (canvas.width / bufferLength) * 2.5;
            let barHeight;
            let x = 0;

            for (let i = 0; i < bufferLength; i++) {
                barHeight = (dataArray[i] / 255) * canvas.height;

                // Color gradient based on frequency
                const hue = (i / bufferLength) * 360;
                ctx.fillStyle = `hsl(${hue}, 100%, 50%)`;
                
                ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);
                x += barWidth + 1;
            }

            // Draw waveform overlay
            ctx.strokeStyle = '#00ff00';
            ctx.lineWidth = 2;
            ctx.beginPath();

            const sliceWidth = canvas.width / bufferLength;
            let waveX = 0;

            for (let i = 0; i < bufferLength; i++) {
                const v = dataArray[i] / 128.0;
                const y = v * canvas.height / 2;

                if (i === 0) {
                    ctx.moveTo(waveX, y);
                } else {
                    ctx.lineTo(waveX, y);
                }

                waveX += sliceWidth;
            }

            ctx.stroke();

            animationRef.current = requestAnimationFrame(draw);
        };

        draw();
    };

    const cleanup = () => {
        if (animationRef.current) {
            cancelAnimationFrame(animationRef.current);
        }
        if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
            audioContextRef.current.close();
        }
        setIsInitialized(false);
        setAudioLevel(0);
        console.log("🔍 [DEBUG] SpectralAnalyzer: Cleaned up");
    };

    if (!isActive) {
        return null;
    }

    return (
        <div className={cn("bg-black rounded-lg p-4 border-2 border-green-500", className)}>
            <div className="flex justify-between items-center mb-2">
                <h3 className="text-green-400 font-mono text-sm font-bold">🎤 Audio Analyzer</h3>
                <div className="flex items-center gap-2">
                    <span className="text-green-400 font-mono text-xs">Level:</span>
                    <span className="text-green-300 font-mono text-xs font-bold">{audioLevel}</span>
                    <div className={cn(
                        "w-3 h-3 rounded-full",
                        isInitialized ? "bg-green-500 animate-pulse" : "bg-red-500"
                    )} />
                </div>
            </div>
            
            <canvas
                ref={canvasRef}
                width={400}
                height={100}
                className="w-full h-24 border border-green-600 rounded bg-black"
            />
            
            <div className="mt-2 text-xs text-green-400 font-mono">
                {!isInitialized ? (
                    "Initializing audio analyzer..."
                ) : audioLevel > 10 ? (
                    "🎵 Audio detected - Good signal!"
                ) : (
                    "🔇 Low/No audio - Check microphone"
                )}
            </div>
        </div>
    );
}
