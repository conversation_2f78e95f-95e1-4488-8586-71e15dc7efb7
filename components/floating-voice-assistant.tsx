"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON> } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import AudioVisualizer from "./audio-visualizer"
import { createAudioChunker, createStreamingTranscription } from "@/utils/audio-utils"

interface FloatingVoiceAssistantProps {
  onTranscriptionComplete: (fields: Record<string, string>) => void
  onConfirm: () => void
  requiredFields: string[]
}

export default function FloatingVoiceAssistant({
  onTranscriptionComplete,
  onConfirm,
  requiredFields,
}: FloatingVoiceAssistantProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [transcript, setTranscript] = useState<string>("")
  const [liveTranscript, setLiveTranscript] = useState<string>("Listening...")
  const [recognizedFields, setRecognizedFields] = useState<Record<string, string>>({})
  const streamRef = useRef<MediaStream | null>(null)
  const audioChunkerRef = useRef<ReturnType<typeof createAudioChunker> | null>(null)

  // Check if all required fields are filled
  const allFieldsFilled = requiredFields.every((field) => recognizedFields[field]?.trim())

  // Start recording when the assistant is opened
  useEffect(() => {
    if (isOpen && !isRecording && !isProcessing) {
      startRecording()
    }
  }, [isOpen, isRecording, isProcessing])

  // Clean up when component unmounts
  useEffect(() => {
    return () => {
      stopRecording()
    }
  }, [])

  // Extract fields from transcription text
  const extractFields = (text: string): Record<string, string> => {
    const fields: Record<string, string> = {}
    const lowerText = text.toLowerCase()

    // Name extraction
    const namePatterns = [
      /my name is ([^,.]+)/i,
      /name is ([^,.]+)/i,
      /i am ([^,.]+)/i,
      /i'm ([^,.]+)/i,
      /([^,.]+) is my name/i,
    ]

    for (const pattern of namePatterns) {
      const match = text.match(pattern)
      if (match && match[1]) {
        fields.name = match[1].trim()
        break
      }
    }

    // Email extraction
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/
    const emailMatch = text.match(emailPattern)
    if (emailMatch) {
      fields.email = emailMatch[0]
    }

    // Phone extraction
    const phonePatterns = [
      /(\d{4}\s?\d{3}\s?\d{3})/, // 0412 345 678
      /(\d{4}\s?\d{6})/, // 0412 345678
      /(\d{10})/, // 0412345678
      /(\d{4}[-\s]?\d{3}[-\s]?\d{3})/, // 0412-345-678
    ]

    for (const pattern of phonePatterns) {
      const match = text.match(pattern)
      if (match && match[1]) {
        fields.phone = match[1].replace(/[-\s]/g, " ").trim()
        break
      }
    }

    // Address extraction
    if (lowerText.includes("address") || lowerText.includes("live at") || lowerText.includes("located at")) {
      const addressPatterns = [
        /(?:my address is|address is|i live at|located at) ([^.]+)/i,
        /(?:address|location)(?:\s+is|:)?\s+([^.]+)/i,
      ]

      for (const pattern of addressPatterns) {
        const match = text.match(pattern)
        if (match && match[1]) {
          fields.address = match[1].trim()
          break
        }
      }
    }

    // Property address extraction (if different from address)
    if (lowerText.includes("property") || lowerText.includes("fence location") || lowerText.includes("site")) {
      const propertyPatterns = [
        /(?:property address is|property is at|fence location is|site is at) ([^.]+)/i,
        /(?:property|site|fence location)(?:\s+is|:)?\s+([^.]+)/i,
      ]

      for (const pattern of propertyPatterns) {
        const match = text.match(pattern)
        if (match && match[1] && !fields.address?.includes(match[1])) {
          fields.propertyAddress = match[1].trim()
          break
        }
      }
    }

    // Additional notes extraction
    if (lowerText.includes("note") || lowerText.includes("additional") || lowerText.includes("comment")) {
      const notesPatterns = [
        /(?:notes|additional notes|comments)(?:\s+are|:)?\s+([^.]+)/i,
        /(?:please note|note that) ([^.]+)/i,
      ]

      for (const pattern of notesPatterns) {
        const match = text.match(pattern)
        if (match && match[1]) {
          fields.additionalNotes = match[1].trim()
          break
        }
      }
    }

    return fields
  }

  // Handle partial transcription results
  const handlePartialResult = (text: string) => {
    setLiveTranscript(text)
  }

  // Handle final transcription results
  const handleFinalResult = (text: string) => {
    // Extract fields from the transcription
    const extractedFields = extractFields(text)
    
    // Append new transcript to existing transcript
    setTranscript((prev) => {
      const newTranscript = prev ? `${prev}\n${text}` : text
      return newTranscript
    })

    // Reset live transcript
    setLiveTranscript("Listening...")

    if (Object.keys(extractedFields).length > 0) {
      // Update recognized fields
      const updatedFields = { ...recognizedFields, ...extractedFields }
      setRecognizedFields(updatedFields)

      // Notify parent component of new fields
      onTranscriptionComplete(updatedFields)

      // Check for confirmation to proceed
      const lowerText = text.toLowerCase()
      if (
        requiredFields.every((field) => updatedFields[field]?.trim()) &&
        (lowerText.includes("yes") ||
          lowerText.includes("confirm") ||
          lowerText.includes("proceed") ||
          lowerText.includes("next") ||
          lowerText.includes("continue"))
      ) {
        onConfirm()
        stopRecording()
        setIsOpen(false)
      }
    }
  }

  // Handle transcription errors
  const handleTranscriptionError = (error: string) => {
    console.error("Transcription error:", error)
    setError(error)
  }

  const startRecording = async () => {
    setError(null)
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      streamRef.current = stream
      
      // Create audio chunker for real-time processing
      audioChunkerRef.current = createAudioChunker(
        (chunk: Blob) => {
          // Process each audio chunk in real-time
          createStreamingTranscription(
            chunk,
            "form",
            handlePartialResult,
            handleFinalResult,
            handleTranscriptionError
          )
        },
        1000 // 1-second chunks for real-time feel
      )
      
      audioChunkerRef.current.startChunking(stream)
      setIsRecording(true)
      setLiveTranscript("Listening...")
    } catch (err) {
      console.error("Error accessing microphone:", err)
      setError(err instanceof Error ? err.message : "Could not access microphone. Please check permissions.")
      setIsOpen(false)
    }
  }


  const stopRecording = () => {
    // Stop the audio chunker
    if (audioChunkerRef.current) {
      audioChunkerRef.current.stopChunking()
      audioChunkerRef.current = null
    }

    // Stop all tracks in the stream to release the microphone
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop())
      streamRef.current = null
    }
    
    setIsRecording(false)
  }

  const toggleAssistant = () => {
    if (isOpen) {
      stopRecording()
    } else {
      setTranscript("")
      setLiveTranscript("Listening...")
    }
    setIsOpen(!isOpen)
  }

  return (
    <>
      <div className="fixed bottom-6 right-6 z-50">
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white rounded-lg shadow-lg mb-4 overflow-hidden"
              style={{ width: "300px" }}
            >
              <div className="p-4 border-b">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium">Voice Assistant</h3>
                  <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {isRecording && (
                <div className="px-4 pt-4">
                  <AudioVisualizer audioStream={streamRef.current} isActive={isRecording} />
                  <div className="mt-2 text-sm text-center text-gray-500">
                    {isProcessing ? "Processing your voice..." : "Speak naturally to fill in the form"}
                  </div>
                </div>
              )}

              <div className="p-4 max-h-[300px] overflow-y-auto">
                {isRecording && (
                  <div className="mb-4">
                    <p className="text-sm font-medium mb-2">I'm hearing:</p>
                    <div className="bg-blue-50 p-3 rounded-md text-sm">
                      <p className="italic">{liveTranscript}</p>
                    </div>
                  </div>
                )}

                {transcript ? (
                  <div>
                    <p className="text-sm font-medium mb-2">Transcript:</p>
                    <div className="bg-gray-50 p-3 rounded-md text-sm">
                      {transcript.split("\n").map((line, i) => (
                        <p key={i} className="mb-1">
                          {line}
                        </p>
                      ))}
                    </div>

                    {Object.keys(recognizedFields).length > 0 && (
                      <div className="mt-4">
                        <p className="text-sm font-medium mb-2">Recognized Information:</p>
                        <div className="bg-green-50 p-3 rounded-md text-sm">
                          {Object.entries(recognizedFields).map(([key, value]) => (
                            <p key={key} className="mb-1">
                              <span className="font-medium capitalize">{key}:</span> {value}
                            </p>
                          ))}
                        </div>
                      </div>
                    )}

                    {allFieldsFilled && (
                      <div className="mt-4 bg-blue-50 p-3 rounded-md text-sm">
                        <p>All required information collected! Say "yes" to continue.</p>
                      </div>
                    )}
                  </div>
                ) : (
                  !isRecording && (
                    <div className="text-center py-4 text-gray-500">
                      <p>Tap the button below to start speaking</p>
                    </div>
                  )
                )}

                {error && <div className="mt-4 bg-red-50 p-3 rounded-md text-sm text-red-600">{error}</div>}
              </div>

              <div className="p-4 bg-gray-50 border-t">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-500">
                    {isRecording ? (
                      <div className="flex items-center">
                        <span className="relative flex h-3 w-3 mr-2">
                          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                          <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                        </span>
                        {isProcessing ? "Processing..." : "Listening..."}
                      </div>
                    ) : (
                      "Ready"
                    )}
                  </div>
                  <Button variant="outline" size="sm" onClick={toggleAssistant} disabled={isProcessing}>
                    {isRecording ? "Stop" : "Start"} Recording
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button
            size="lg"
            className={`h-14 w-14 rounded-full shadow-lg ${
              isOpen ? "bg-red-500 hover:bg-red-600" : "bg-[#5d8c62] hover:bg-[#4d7352]"
            }`}
            onClick={toggleAssistant}
          >
            {isOpen ? (
              isProcessing ? (
                <span className="animate-pulse">
                  <MicOff className="h-6 w-6" />
                </span>
              ) : (
                <MicOff className="h-6 w-6" />
              )
            ) : (
              <Mic className="h-6 w-6" />
            )}
          </Button>
        </motion.div>
      </div>
    </>
  )
}
