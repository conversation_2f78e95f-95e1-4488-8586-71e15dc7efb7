"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"

export default function TestimonialsSection() {
  const [activeIndex, setActiveIndex] = useState(0)

  const testimonials = [
    {
      quote: "Excellent to deal with, prompt & professional service, and a great product delivered and installed.",
      author: "Scott Shallies",
      location: "Google Review",
    },
    {
      quote:
        "From humble beginnings as a gate manufacturer, they've evolved into a premier full-service fencing company. Their expert team delivers custom-designed fences and gates tailored to your unique needs.",
      author: "Satisfied Customer",
      location: "Macedon Ranges",
    },
    {
      quote:
        "They're more than just a fencing company. They're partners in creating stunning outdoor spaces. From initial design to final installation, their team is committed to exceeding your expectations.",
      author: "Happy Client",
      location: "Victoria",
    },
  ]

  const nextTestimonial = () => {
    setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setActiveIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length)
  }

  return (
    <section id="testimonials" className="py-24 px-6 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start mb-16">
          <h2 className="text-3xl font-light">TESTIMONIALS</h2>
          <p className="max-w-xl mt-4 md:mt-0 text-gray-600">
            Don't just take our word for it. Here's what our clients have to say about our timber fencing solutions.
          </p>
        </div>

        <div className="relative">
          <Card className="border-none shadow-lg">
            <CardContent className="p-8 md:p-12">
              <div className="text-[#5d8c62] mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                </svg>
              </div>
              <p className="text-xl md:text-2xl mb-8">{testimonials[activeIndex].quote}</p>
              <div>
                <p className="font-medium">{testimonials[activeIndex].author}</p>
                <p className="text-gray-600">{testimonials[activeIndex].location}</p>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-center mt-8 space-x-4">
            <button
              onClick={prevTestimonial}
              className="h-10 w-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <div className="flex items-center space-x-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActiveIndex(index)}
                  className={`h-2.5 rounded-full transition-all ${
                    index === activeIndex ? "w-8 bg-[#5d8c62]" : "w-2.5 bg-gray-300"
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>
            <button
              onClick={nextTestimonial}
              className="h-10 w-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}
