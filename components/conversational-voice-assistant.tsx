"use client";

import { cn } from "@/lib/utils";
import { Mi<PERSON>, MicOff, Volume2, VolumeX } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";

interface ConversationMessage {
    role: "user" | "assistant";
    content: string;
    timestamp: Date;
    action?: string;
    field?: string;
    value?: string;
    next_step?: string;
}

interface ConversationalVoiceAssistantProps {
    agentId: string;
    onFieldUpdate: (field: string, value: string) => void;
    onStepComplete: () => void;
    onConversationUpdate: (messages: ConversationMessage[]) => void;
    currentStep: "contact" | "design";
    className?: string;
}

export default function ConversationalVoiceAssistant({
    agentId,
    onFieldUpdate,
    onStepComplete,
    onConversationUpdate,
    currentStep,
    className,
}: ConversationalVoiceAssistantProps) {
    const [isConnected, setIsConnected] = useState(false);
    const [isListening, setIsListening] = useState(false);
    const [isSpeaking, setIsSpeaking] = useState(false);
    const [isMuted, setIsMuted] = useState(false);
    const [conversation, setConversation] = useState<ConversationMessage[]>([]);
    const [connectionStatus, setConnectionStatus] = useState<string>("Disconnected");
    const [error, setError] = useState<string | null>(null);

    const wsRef = useRef<WebSocket | null>(null);
    const audioContextRef = useRef<AudioContext | null>(null);
    const mediaStreamRef = useRef<MediaStream | null>(null);
    const audioElementRef = useRef<HTMLAudioElement | null>(null);

    // Initialize audio context
    useEffect(() => {
        const initAudio = async () => {
            try {
                audioContextRef.current = new AudioContext();
                audioElementRef.current = new Audio();
                audioElementRef.current.autoplay = true;
            } catch (error) {
                console.error("Failed to initialize audio:", error);
                setError("Audio initialization failed");
            }
        };

        initAudio();

        return () => {
            if (audioContextRef.current) {
                audioContextRef.current.close();
            }
        };
    }, []);

    // Connect to ElevenLabs Conversational AI via MCP
    const connectToAgent = async () => {
        try {
            setError(null);
            setConnectionStatus("Connecting...");

            // Simulate connection for now - in production this would use the actual ElevenLabs API
            setIsConnected(true);
            setConnectionStatus("Connected");

            // Add initial assistant message
            const initialMessage: ConversationMessage = {
                role: "assistant",
                content: currentStep === "contact"
                    ? "Hi! I'm here to help you get a quote for your fencing project. I'll guide you through collecting your details and designing your fence layout. What's your name?"
                    : "Great! Now let's design your fence layout on the map. I can help guide you through drawing fence lines and placing gates. Do you have any questions about the map tools?",
                timestamp: new Date(),
                action: "collect_info",
                field: currentStep === "contact" ? "name" : "fence_design",
                next_step: currentStep === "contact" ? "collect_name" : "guide_design"
            };

            setConversation([initialMessage]);
            onConversationUpdate([initialMessage]);

        } catch (error) {
            console.error("Failed to connect to agent:", error);
            setError(error instanceof Error ? error.message : "Connection failed");
            setConnectionStatus("Error");
        }
    };

    // Handle messages from the agent
    const handleAgentMessage = async (data: any) => {
        switch (data.type) {
            case "agent_response":
                setIsSpeaking(true);

                // Parse the agent's structured response
                let agentResponse;
                try {
                    agentResponse = JSON.parse(data.agent_response);
                } catch {
                    // Fallback if response isn't JSON
                    agentResponse = {
                        message: data.agent_response,
                        action: "continue",
                        field: null,
                        value: null,
                        next_step: "continue"
                    };
                }

                // Add to conversation
                const assistantMessage: ConversationMessage = {
                    role: "assistant",
                    content: agentResponse.message,
                    timestamp: new Date(),
                    action: agentResponse.action,
                    field: agentResponse.field,
                    value: agentResponse.value,
                    next_step: agentResponse.next_step,
                };

                setConversation(prev => {
                    const updated = [...prev, assistantMessage];
                    onConversationUpdate(updated);
                    return updated;
                });

                // Handle field updates
                if (agentResponse.field && agentResponse.value) {
                    onFieldUpdate(agentResponse.field, agentResponse.value);
                }

                // Handle step completion
                if (agentResponse.action === "complete") {
                    onStepComplete();
                }

                break;

            case "audio":
                // Play audio response
                if (data.audio_data && !isMuted) {
                    try {
                        const audioBlob = new Blob([
                            Uint8Array.from(atob(data.audio_data), c => c.charCodeAt(0))
                        ], { type: "audio/mpeg" });

                        const audioUrl = URL.createObjectURL(audioBlob);
                        if (audioElementRef.current) {
                            audioElementRef.current.src = audioUrl;
                            audioElementRef.current.onended = () => {
                                setIsSpeaking(false);
                                URL.revokeObjectURL(audioUrl);
                            };
                            await audioElementRef.current.play();
                        }
                    } catch (error) {
                        console.error("Error playing audio:", error);
                        setIsSpeaking(false);
                    }
                }
                break;

            case "user_transcript":
                // Add user message to conversation
                const userMessage: ConversationMessage = {
                    role: "user",
                    content: data.user_transcript,
                    timestamp: new Date(),
                };

                setConversation(prev => {
                    const updated = [...prev, userMessage];
                    onConversationUpdate(updated);
                    return updated;
                });
                break;

            case "listening_start":
                setIsListening(true);
                break;

            case "listening_end":
                setIsListening(false);
                break;

            default:
                console.log("Unknown message type:", data.type);
        }
    };

    // Start/stop listening
    const toggleListening = async () => {
        if (!isConnected) {
            await connectToAgent();
            return;
        }

        if (isListening) {
            // Stop listening
            stopRecording();
        } else {
            // Start listening
            startRecording();
        }
    };

    // Start recording audio
    const startRecording = async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            mediaStreamRef.current = stream;

            const mediaRecorder = new MediaRecorder(stream);
            const audioChunks: Blob[] = [];

            mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    audioChunks.push(event.data);
                }
            };

            mediaRecorder.onstop = async () => {
                const audioBlob = new Blob(audioChunks, { type: "audio/webm" });
                await processAudioWithAgent(audioBlob);

                // Stop all tracks
                stream.getTracks().forEach(track => track.stop());
                setIsListening(false);
            };

            mediaRecorder.start();
            setIsListening(true);

            // Auto-stop after 10 seconds
            setTimeout(() => {
                if (mediaRecorder.state === "recording") {
                    mediaRecorder.stop();
                }
            }, 10000);

        } catch (error) {
            console.error("Error accessing microphone:", error);
            setError("Microphone access denied");
        }
    };

    // Stop recording
    const stopRecording = () => {
        if (mediaStreamRef.current) {
            mediaStreamRef.current.getTracks().forEach(track => track.stop());
        }
        setIsListening(false);
    };

    // Process audio with the conversational agent
    const processAudioWithAgent = async (audioBlob: Blob) => {
        try {
            setIsSpeaking(true);

            // Create form data for transcription
            const formData = new FormData();
            formData.append("audio", audioBlob, "recording.webm");

            // Use the existing transcription API but with conversational context
            const response = await fetch("/api/transcribe-conversational", {
                method: "POST",
                body: formData,
                headers: {
                    "X-Agent-ID": agentId,
                    "X-Current-Step": currentStep,
                    "X-Conversation-Context": JSON.stringify(conversation.slice(-3)) // Last 3 messages for context
                }
            });

            if (!response.ok) {
                throw new Error(`Transcription failed: ${response.statusText}`);
            }

            const data = await response.json();

            // Add user message to conversation
            const userMessage: ConversationMessage = {
                role: "user",
                content: data.user_text,
                timestamp: new Date(),
            };

            // Add assistant response
            const assistantMessage: ConversationMessage = {
                role: "assistant",
                content: data.agent_response.message,
                timestamp: new Date(),
                action: data.agent_response.action,
                field: data.agent_response.field,
                value: data.agent_response.value,
                next_step: data.agent_response.next_step,
            };

            const updatedConversation = [...conversation, userMessage, assistantMessage];
            setConversation(updatedConversation);
            onConversationUpdate(updatedConversation);

            // Handle field updates
            if (data.agent_response.field && data.agent_response.value) {
                onFieldUpdate(data.agent_response.field, data.agent_response.value);
            }

            // Handle step completion
            if (data.agent_response.action === "complete") {
                onStepComplete();
            }

            // Play audio response if available
            if (data.audio_response && !isMuted) {
                try {
                    const audioUrl = `data:audio/mpeg;base64,${data.audio_response}`;
                    if (audioElementRef.current) {
                        audioElementRef.current.src = audioUrl;
                        audioElementRef.current.onended = () => setIsSpeaking(false);
                        await audioElementRef.current.play();
                    }
                } catch (audioError) {
                    console.error("Error playing audio:", audioError);
                    setIsSpeaking(false);
                }
            } else {
                setIsSpeaking(false);
            }

        } catch (error) {
            console.error("Error processing audio with agent:", error);
            setError("Failed to process voice input");
            setIsSpeaking(false);
        }
    };

    // Disconnect from agent
    const disconnect = () => {
        if (wsRef.current) {
            wsRef.current.close();
        }
        if (mediaStreamRef.current) {
            mediaStreamRef.current.getTracks().forEach(track => track.stop());
        }
        setIsConnected(false);
        setIsListening(false);
        setIsSpeaking(false);
    };

    // Toggle mute
    const toggleMute = () => {
        setIsMuted(!isMuted);
        if (audioElementRef.current) {
            audioElementRef.current.muted = !isMuted;
        }
    };

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            disconnect();
        };
    }, []);

    return (
        <div className={cn("flex flex-col items-center gap-4", className)}>
            {/* Main Voice Button */}
            <div className="relative">
                <Button
                    size="lg"
                    onClick={toggleListening}
                    disabled={isSpeaking}
                    className={cn(
                        "h-16 w-16 rounded-full transition-all duration-300 shadow-lg",
                        isConnected
                            ? isListening
                                ? "bg-red-500 hover:bg-red-600 animate-pulse"
                                : isSpeaking
                                ? "bg-blue-500 hover:bg-blue-600"
                                : "bg-[#5d8c62] hover:bg-[#4d7352]"
                            : "bg-zinc-400 hover:bg-zinc-500"
                    )}
                >
                    {isSpeaking ? (
                        <Volume2 className="h-6 w-6 text-white" />
                    ) : isListening ? (
                        <MicOff className="h-6 w-6 text-white" />
                    ) : (
                        <Mic className="h-6 w-6 text-white" />
                    )}
                </Button>

                {/* Status indicator */}
                {isConnected && (
                    <div className="absolute -top-1 -right-1 h-4 w-4 bg-green-500 rounded-full border-2 border-white"></div>
                )}
            </div>

            {/* Controls */}
            <div className="flex items-center gap-2">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleMute}
                    className="h-8 w-8 p-0"
                >
                    {isMuted ? (
                        <VolumeX className="h-4 w-4" />
                    ) : (
                        <Volume2 className="h-4 w-4" />
                    )}
                </Button>

                {isConnected && (
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={disconnect}
                        className="text-xs px-2"
                    >
                        Disconnect
                    </Button>
                )}
            </div>

            {/* Status */}
            <div className="text-center">
                <p className={cn(
                    "text-sm font-medium",
                    isConnected ? "text-green-600" : "text-zinc-500"
                )}>
                    {connectionStatus}
                </p>

                {isListening && (
                    <p className="text-xs text-blue-600 animate-pulse">
                        Listening...
                    </p>
                )}

                {isSpeaking && (
                    <p className="text-xs text-purple-600 animate-pulse">
                        Speaking...
                    </p>
                )}

                {error && (
                    <p className="text-xs text-red-600 mt-1">
                        {error}
                    </p>
                )}
            </div>

            {/* Conversation Display */}
            {conversation.length > 0 && (
                <div className="w-full max-w-md max-h-32 overflow-y-auto bg-zinc-50 dark:bg-zinc-800 rounded-lg p-3">
                    <div className="space-y-2">
                        {conversation.slice(-3).map((message, index) => (
                            <div
                                key={index}
                                className={cn(
                                    "text-xs p-2 rounded",
                                    message.role === "user"
                                        ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 ml-4"
                                        : "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 mr-4"
                                )}
                            >
                                <span className="font-medium">
                                    {message.role === "user" ? "You" : "Assistant"}:
                                </span>{" "}
                                {message.content}
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
}
