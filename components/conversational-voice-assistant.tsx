"use client";

import { cn } from "@/lib/utils";
import { Mi<PERSON>, MicOff, Volume2, VolumeX } from "lucide-react";
import { useState, useEffect, useCallback, useRef } from "react";
import { Button } from "@/components/ui/button";
import { useConversation } from "@elevenlabs/react";
import SpectralAnalyzer from "@/components/spectral-analyzer";

interface ConversationMessage {
    role: "user" | "assistant";
    content: string;
    timestamp: Date;
    action?: string;
    field?: string;
    value?: string;
    next_step?: string;
}

interface ConversationalVoiceAssistantProps {
    agentId: string;
    onFieldUpdate: (field: string, value: string) => void;
    onStepComplete: () => void;
    onConversationUpdate: (messages: ConversationMessage[]) => void;
    currentStep: "contact" | "design";
    className?: string;
}

export default function ConversationalVoiceAssistant({
    agentId,
    onFieldUpdate,
    onStepComplete,
    onConversationUpdate,
    currentStep,
    className,
}: ConversationalVoiceAssistantProps) {
    const [conversationMessages, setConversationMessages] = useState<ConversationMessage[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [isMuted, setIsMuted] = useState(false);
    const [audioStream, setAudioStream] = useState<MediaStream | null>(null);
    const [showSpectralAnalyzer, setShowSpectralAnalyzer] = useState(false);
    const audioStreamRef = useRef<MediaStream | null>(null);

    // Use the official ElevenLabs React hook
    const conversation = useConversation({
        onConnect: () => {
            console.log('Connected to ElevenLabs Conversational AI');
            setError(null);
        },
        onDisconnect: () => {
            console.log('Disconnected from ElevenLabs Conversational AI');
        },
        onMessage: (message) => {
            console.log('Received message:', message);
            handleConversationMessage(message);
        },
        onError: (error) => {
            console.error('Conversation error:', error);
            setError(error.message || 'An error occurred');
        },
    });

    // Handle conversation messages and extract field data
    const handleConversationMessage = useCallback((message: any) => {
        console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Received message:", message);

        if (message.type === 'user_transcript' || message.type === 'user_message') {
            const userText = message.user_transcript || message.content || message.text;
            console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Processing user text:", userText);

            const userMessage: ConversationMessage = {
                role: "user",
                content: userText,
                timestamp: new Date(),
            };

            setConversationMessages(prev => {
                const updated = [...prev, userMessage];
                onConversationUpdate(updated);
                return updated;
            });

            // Extract fields from user input in real-time
            extractFieldsFromUserInput(userText);
        }

        if (message.type === 'agent_response' || message.type === 'assistant_message') {
            const agentText = message.agent_response || message.content || message.text;
            console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Processing agent response:", agentText);

            const agentMessage: ConversationMessage = {
                role: "assistant",
                content: agentText,
                timestamp: new Date(),
            };

            setConversationMessages(prev => {
                const updated = [...prev, agentMessage];
                onConversationUpdate(updated);
                return updated;
            });
        }

        // Handle any other message types that might contain user input
        if (message.user_transcript || message.transcript) {
            const transcript = message.user_transcript || message.transcript;
            console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Processing transcript:", transcript);
            extractFieldsFromUserInput(transcript);
        }
    }, [onConversationUpdate, onFieldUpdate, onStepComplete, currentStep]);

    // Extract fields from user input in real-time
    const extractFieldsFromUserInput = useCallback((userText: string) => {
        if (!userText || currentStep !== "contact") return;

        console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Extracting fields from:", userText);
        const lowerText = userText.toLowerCase();

        // Extract name patterns
        const namePatterns = [
            /my name is ([^,.!?]+)/i,
            /name is ([^,.!?]+)/i,
            /i am ([^,.!?]+)/i,
            /i'm ([^,.!?]+)/i,
            /this is ([^,.!?]+)/i,
            /call me ([^,.!?]+)/i,
        ];

        for (const pattern of namePatterns) {
            const match = userText.match(pattern);
            if (match && match[1]) {
                const name = match[1].trim();
                console.log("✅ [DEBUG] ConversationalVoiceAssistant: Extracted name:", name);
                onFieldUpdate("name", name);
            }
        }

        // Extract email
        const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
        const emailMatch = userText.match(emailPattern);
        if (emailMatch) {
            console.log("✅ [DEBUG] ConversationalVoiceAssistant: Extracted email:", emailMatch[0]);
            onFieldUpdate("email", emailMatch[0]);
        }

        // Extract phone
        const phonePatterns = [
            /(\d{4}\s?\d{3}\s?\d{3})/, // 0412 345 678
            /(\d{4}\s?\d{6})/, // 0412 345678
            /(\d{10})/, // 0412345678
            /(\+61\s?\d{1}\s?\d{4}\s?\d{4})/, // +61 4 1234 5678
        ];

        for (const pattern of phonePatterns) {
            const match = userText.match(pattern);
            if (match && match[1]) {
                const phone = match[1].replace(/[-\s]/g, " ").trim();
                console.log("✅ [DEBUG] ConversationalVoiceAssistant: Extracted phone:", phone);
                onFieldUpdate("phone", phone);
            }
        }

        // Extract address
        if (lowerText.includes("street") || lowerText.includes("road") || lowerText.includes("avenue") ||
            lowerText.includes("drive") || lowerText.includes("vic") || lowerText.includes("nsw") ||
            lowerText.includes("qld") || lowerText.includes("wa") || lowerText.includes("sa") ||
            lowerText.includes("tas") || lowerText.includes("nt") || lowerText.includes("act") ||
            /\d+\s+[a-z\s]+/i.test(userText)) {
            console.log("✅ [DEBUG] ConversationalVoiceAssistant: Extracted address:", userText);
            onFieldUpdate("propertyAddress", userText);
        }

        // Check for completion signals
        if (lowerText.includes("done") || lowerText.includes("finished") || lowerText.includes("complete") ||
            lowerText.includes("that's all") || lowerText.includes("next step")) {
            console.log("✅ [DEBUG] ConversationalVoiceAssistant: Completion signal detected");
            onStepComplete();
        }
    }, [currentStep, onFieldUpdate, onStepComplete]);

    // Extract form fields from conversation (legacy function)
    const extractFieldsFromResponse = useCallback((agentResponse: string, userTranscript: string) => {
        if (!userTranscript) return;

        const lowerText = userTranscript.toLowerCase();

        if (currentStep === "contact") {
            // Extract name
            const namePatterns = [
                /my name is ([^,.]+)/i,
                /name is ([^,.]+)/i,
                /i am ([^,.]+)/i,
                /i'm ([^,.]+)/i,
            ];

            for (const pattern of namePatterns) {
                const match = userTranscript.match(pattern);
                if (match && match[1]) {
                    const name = match[1].trim();
                    onFieldUpdate("name", name);
                    return;
                }
            }

            // Extract email
            const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
            const emailMatch = userTranscript.match(emailPattern);
            if (emailMatch) {
                onFieldUpdate("email", emailMatch[0]);
                return;
            }

            // Extract phone
            const phonePatterns = [
                /(\d{4}\s?\d{3}\s?\d{3})/, // 0412 345 678
                /(\d{4}\s?\d{6})/, // 0412 345678
                /(\d{10})/, // 0412345678
            ];

            for (const pattern of phonePatterns) {
                const match = userTranscript.match(pattern);
                if (match && match[1]) {
                    const phone = match[1].replace(/[-\s]/g, " ").trim();
                    onFieldUpdate("phone", phone);
                    return;
                }
            }

            // Extract address
            if (lowerText.includes("street") || lowerText.includes("road") || lowerText.includes("avenue") ||
                lowerText.includes("drive") || lowerText.includes("vic") || lowerText.includes("nsw") ||
                lowerText.includes("qld") || lowerText.includes("wa") || lowerText.includes("sa") ||
                lowerText.includes("tas") || lowerText.includes("nt") || lowerText.includes("act")) {
                onFieldUpdate("propertyAddress", userTranscript);
                return;
            }
        }

        // Check for completion signals
        if (lowerText.includes("done") || lowerText.includes("finished") || lowerText.includes("complete")) {
            onStepComplete();
        }
    }, [currentStep, onFieldUpdate, onStepComplete]);

    // Get signed URL for secure connection
    const getSignedUrl = useCallback(async (): Promise<string> => {
        console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Getting signed URL for agent:", agentId);

        try {
            const url = `/api/get-signed-url?agent_id=${agentId}`;
            console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Fetching from:", url);

            const response = await fetch(url);
            console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Response status:", response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error("❌ [DEBUG] ConversationalVoiceAssistant: Failed to get signed URL:", {
                    status: response.status,
                    statusText: response.statusText,
                    errorText: errorText,
                    url: url
                });
                throw new Error(`Failed to get signed URL: ${response.statusText} - ${errorText}`);
            }

            const data = await response.json();
            console.log("✅ [DEBUG] ConversationalVoiceAssistant: Signed URL response:", data);

            if (!data.signedUrl) {
                console.error("❌ [DEBUG] ConversationalVoiceAssistant: No signedUrl in response:", data);
                throw new Error("No signed URL returned from API");
            }

            return data.signedUrl;
        } catch (error) {
            console.error("❌ [DEBUG] ConversationalVoiceAssistant: Error in getSignedUrl:", error);
            throw error;
        }
    }, [agentId]);

    // Start conversation with ElevenLabs
    const startConversation = useCallback(async () => {
        console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Starting conversation...");

        try {
            setError(null);
            console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Cleared error state");

            // Request microphone permission and capture stream
            console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Requesting microphone permission...");
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 44100
                }
            });
            console.log("✅ [DEBUG] ConversationalVoiceAssistant: Microphone permission granted");

            // Store audio stream for spectral analyzer
            setAudioStream(stream);
            audioStreamRef.current = stream;
            setShowSpectralAnalyzer(true);
            console.log("✅ [DEBUG] ConversationalVoiceAssistant: Audio stream captured for analyzer");

            // Get signed URL for secure connection
            console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Getting signed URL...");
            const signedUrl = await getSignedUrl();
            console.log("✅ [DEBUG] ConversationalVoiceAssistant: Got signed URL:", signedUrl);

            // Start the conversation with the signed URL
            console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Starting session with ElevenLabs...");
            await conversation.startSession({
                signedUrl: signedUrl,
            });
            console.log("✅ [DEBUG] ConversationalVoiceAssistant: Session started successfully");

            // Add initial assistant message to our local state
            const initialMessage: ConversationMessage = {
                role: "assistant",
                content: currentStep === "contact"
                    ? "Hi, welcome to our AI fence quoting tool! If you can give me your name, email, and address of the property, we can get a quote done for you right away."
                    : "Great! Now let's design your fence layout on the map. I can help guide you through drawing fence lines and placing gates. Do you have any questions about the map tools?",
                timestamp: new Date(),
            };

            console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Adding initial message:", initialMessage);
            setConversationMessages([initialMessage]);
            onConversationUpdate([initialMessage]);
            console.log("✅ [DEBUG] ConversationalVoiceAssistant: Conversation started successfully");

        } catch (error) {
            console.error("❌ [DEBUG] ConversationalVoiceAssistant: Failed to start conversation:", error);
            setError(error instanceof Error ? error.message : "Failed to start conversation");
        }
    }, [conversation, getSignedUrl, currentStep, onConversationUpdate]);

    // Stop conversation
    const stopConversation = useCallback(async () => {
        console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Stopping conversation...");

        try {
            await conversation.endSession();

            // Clean up audio stream
            if (audioStreamRef.current) {
                audioStreamRef.current.getTracks().forEach(track => {
                    track.stop();
                    console.log("🔍 [DEBUG] ConversationalVoiceAssistant: Stopped audio track");
                });
                audioStreamRef.current = null;
            }

            setAudioStream(null);
            setShowSpectralAnalyzer(false);
            setConversationMessages([]);
            onConversationUpdate([]);
            console.log("✅ [DEBUG] ConversationalVoiceAssistant: Conversation stopped and cleaned up");

        } catch (error) {
            console.error("❌ [DEBUG] ConversationalVoiceAssistant: Failed to stop conversation:", error);
        }
    }, [conversation, onConversationUpdate]);

    // Toggle mute for audio responses
    const toggleMute = useCallback(() => {
        setIsMuted(!isMuted);
    }, [isMuted]);

    // Get connection status
    const isConnected = conversation.status === 'connected';
    const isListening = conversation.status === 'connected' && !conversation.isSpeaking;
    const isSpeaking = conversation.isSpeaking;

    return (
        <div className={cn("flex flex-col items-center gap-4", className)}>
            {/* Main Voice Button */}
            <div className="relative">
                <Button
                    size="lg"
                    onClick={isConnected ? stopConversation : startConversation}
                    disabled={isSpeaking}
                    className={cn(
                        "h-16 w-16 rounded-full transition-all duration-300 shadow-lg",
                        isConnected
                            ? isListening
                                ? "bg-red-500 hover:bg-red-600 animate-pulse"
                                : isSpeaking
                                ? "bg-blue-500 hover:bg-blue-600"
                                : "bg-[#5d8c62] hover:bg-[#4d7352]"
                            : "bg-zinc-400 hover:bg-zinc-500"
                    )}
                >
                    {isSpeaking ? (
                        <Volume2 className="h-6 w-6 text-white" />
                    ) : isListening ? (
                        <MicOff className="h-6 w-6 text-white" />
                    ) : (
                        <Mic className="h-6 w-6 text-white" />
                    )}
                </Button>

                {/* Status indicator */}
                {isConnected && (
                    <div className="absolute -top-1 -right-1 h-4 w-4 bg-green-500 rounded-full border-2 border-white"></div>
                )}
            </div>

            {/* Controls */}
            <div className="flex items-center gap-2">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleMute}
                    className="h-8 w-8 p-0"
                >
                    {isMuted ? (
                        <VolumeX className="h-4 w-4" />
                    ) : (
                        <Volume2 className="h-4 w-4" />
                    )}
                </Button>

                {isConnected && (
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={stopConversation}
                        className="text-xs px-2"
                    >
                        Disconnect
                    </Button>
                )}
            </div>

            {/* Status */}
            <div className="text-center">
                <p className={cn(
                    "text-sm font-medium",
                    isConnected ? "text-green-600" : "text-zinc-500"
                )}>
                    {isConnected ? "Connected" : "Disconnected"}
                </p>

                {isListening && (
                    <p className="text-xs text-blue-600 animate-pulse">
                        Listening...
                    </p>
                )}

                {isSpeaking && (
                    <p className="text-xs text-purple-600 animate-pulse">
                        Speaking...
                    </p>
                )}

                {error && (
                    <p className="text-xs text-red-600 mt-1">
                        {error}
                    </p>
                )}
            </div>

            {/* Spectral Analyzer */}
            {showSpectralAnalyzer && audioStream && (
                <SpectralAnalyzer
                    isActive={showSpectralAnalyzer}
                    audioStream={audioStream}
                    className="w-full max-w-md"
                />
            )}

            {/* Conversation Display */}
            {conversationMessages.length > 0 && (
                <div className="w-full max-w-md max-h-32 overflow-y-auto bg-zinc-50 dark:bg-zinc-800 rounded-lg p-3">
                    <div className="space-y-2">
                        {conversationMessages.slice(-3).map((message, index) => (
                            <div
                                key={index}
                                className={cn(
                                    "text-xs p-2 rounded",
                                    message.role === "user"
                                        ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 ml-4"
                                        : "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 mr-4"
                                )}
                            >
                                <span className="font-medium">
                                    {message.role === "user" ? "You" : "Assistant"}:
                                </span>{" "}
                                {message.content}
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
}
