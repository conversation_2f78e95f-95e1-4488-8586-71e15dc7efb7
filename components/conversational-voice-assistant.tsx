"use client";

import { cn } from "@/lib/utils";
import { <PERSON><PERSON>, <PERSON>c<PERSON>ff, Volume2, VolumeX } from "lucide-react";
import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { useConversation } from "@elevenlabs/react";

interface ConversationMessage {
    role: "user" | "assistant";
    content: string;
    timestamp: Date;
    action?: string;
    field?: string;
    value?: string;
    next_step?: string;
}

interface ConversationalVoiceAssistantProps {
    agentId: string;
    onFieldUpdate: (field: string, value: string) => void;
    onStepComplete: () => void;
    onConversationUpdate: (messages: ConversationMessage[]) => void;
    currentStep: "contact" | "design";
    className?: string;
}

export default function ConversationalVoiceAssistant({
    agentId,
    onFieldUpdate,
    onStepComplete,
    onConversationUpdate,
    currentStep,
    className,
}: ConversationalVoiceAssistantProps) {
    const [conversationMessages, setConversationMessages] = useState<ConversationMessage[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [isMuted, setIsMuted] = useState(false);

    // Use the official ElevenLabs React hook
    const conversation = useConversation({
        onConnect: () => {
            console.log('Connected to ElevenLabs Conversational AI');
            setError(null);
        },
        onDisconnect: () => {
            console.log('Disconnected from ElevenLabs Conversational AI');
        },
        onMessage: (message) => {
            console.log('Received message:', message);
            handleConversationMessage(message);
        },
        onError: (error) => {
            console.error('Conversation error:', error);
            setError(error.message || 'An error occurred');
        },
    });

    // Handle conversation messages and extract field data
    const handleConversationMessage = useCallback((message: any) => {
        if (message.type === 'user_transcript') {
            const userMessage: ConversationMessage = {
                role: "user",
                content: message.user_transcript,
                timestamp: new Date(),
            };
            setConversationMessages(prev => {
                const updated = [...prev, userMessage];
                onConversationUpdate(updated);
                return updated;
            });
        }

        if (message.type === 'agent_response') {
            const agentMessage: ConversationMessage = {
                role: "assistant",
                content: message.agent_response,
                timestamp: new Date(),
            };
            setConversationMessages(prev => {
                const updated = [...prev, agentMessage];
                onConversationUpdate(updated);
                return updated;
            });

            // Try to extract field data from the agent response
            extractFieldsFromResponse(message.agent_response, message.user_transcript);
        }
    }, [onConversationUpdate, onFieldUpdate, onStepComplete, currentStep]);

    // Extract form fields from conversation
    const extractFieldsFromResponse = useCallback((agentResponse: string, userTranscript: string) => {
        if (!userTranscript) return;

        const lowerText = userTranscript.toLowerCase();

        if (currentStep === "contact") {
            // Extract name
            const namePatterns = [
                /my name is ([^,.]+)/i,
                /name is ([^,.]+)/i,
                /i am ([^,.]+)/i,
                /i'm ([^,.]+)/i,
            ];

            for (const pattern of namePatterns) {
                const match = userTranscript.match(pattern);
                if (match && match[1]) {
                    const name = match[1].trim();
                    onFieldUpdate("name", name);
                    return;
                }
            }

            // Extract email
            const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
            const emailMatch = userTranscript.match(emailPattern);
            if (emailMatch) {
                onFieldUpdate("email", emailMatch[0]);
                return;
            }

            // Extract phone
            const phonePatterns = [
                /(\d{4}\s?\d{3}\s?\d{3})/, // 0412 345 678
                /(\d{4}\s?\d{6})/, // 0412 345678
                /(\d{10})/, // 0412345678
            ];

            for (const pattern of phonePatterns) {
                const match = userTranscript.match(pattern);
                if (match && match[1]) {
                    const phone = match[1].replace(/[-\s]/g, " ").trim();
                    onFieldUpdate("phone", phone);
                    return;
                }
            }

            // Extract address
            if (lowerText.includes("street") || lowerText.includes("road") || lowerText.includes("avenue") ||
                lowerText.includes("drive") || lowerText.includes("vic") || lowerText.includes("nsw") ||
                lowerText.includes("qld") || lowerText.includes("wa") || lowerText.includes("sa") ||
                lowerText.includes("tas") || lowerText.includes("nt") || lowerText.includes("act")) {
                onFieldUpdate("propertyAddress", userTranscript);
                return;
            }
        }

        // Check for completion signals
        if (lowerText.includes("done") || lowerText.includes("finished") || lowerText.includes("complete")) {
            onStepComplete();
        }
    }, [currentStep, onFieldUpdate, onStepComplete]);

    // Get signed URL for secure connection
    const getSignedUrl = useCallback(async (): Promise<string> => {
        const response = await fetch(`/api/get-signed-url?agent_id=${agentId}`);
        if (!response.ok) {
            throw new Error(`Failed to get signed URL: ${response.statusText}`);
        }
        const { signedUrl } = await response.json();
        return signedUrl;
    }, [agentId]);

    // Start conversation with ElevenLabs
    const startConversation = useCallback(async () => {
        try {
            setError(null);

            // Request microphone permission
            await navigator.mediaDevices.getUserMedia({ audio: true });

            // Get signed URL for secure connection
            const signedUrl = await getSignedUrl();

            // Start the conversation with the signed URL
            await conversation.startSession({
                signedUrl: signedUrl,
            });

            // Add initial assistant message to our local state
            const initialMessage: ConversationMessage = {
                role: "assistant",
                content: currentStep === "contact"
                    ? "Hi, welcome to our AI fence quoting tool! If you can give me your name, email, and address of the property, we can get a quote done for you right away."
                    : "Great! Now let's design your fence layout on the map. I can help guide you through drawing fence lines and placing gates. Do you have any questions about the map tools?",
                timestamp: new Date(),
            };

            setConversationMessages([initialMessage]);
            onConversationUpdate([initialMessage]);

        } catch (error) {
            console.error("Failed to start conversation:", error);
            setError(error instanceof Error ? error.message : "Failed to start conversation");
        }
    }, [conversation, getSignedUrl, currentStep, onConversationUpdate]);

    // Stop conversation
    const stopConversation = useCallback(async () => {
        try {
            await conversation.endSession();
            setConversationMessages([]);
            onConversationUpdate([]);
        } catch (error) {
            console.error("Failed to stop conversation:", error);
        }
    }, [conversation, onConversationUpdate]);

    // Toggle mute for audio responses
    const toggleMute = useCallback(() => {
        setIsMuted(!isMuted);
    }, [isMuted]);

    // Get connection status
    const isConnected = conversation.status === 'connected';
    const isListening = conversation.status === 'connected' && !conversation.isSpeaking;
    const isSpeaking = conversation.isSpeaking;

    return (
        <div className={cn("flex flex-col items-center gap-4", className)}>
            {/* Main Voice Button */}
            <div className="relative">
                <Button
                    size="lg"
                    onClick={isConnected ? stopConversation : startConversation}
                    disabled={isSpeaking}
                    className={cn(
                        "h-16 w-16 rounded-full transition-all duration-300 shadow-lg",
                        isConnected
                            ? isListening
                                ? "bg-red-500 hover:bg-red-600 animate-pulse"
                                : isSpeaking
                                ? "bg-blue-500 hover:bg-blue-600"
                                : "bg-[#5d8c62] hover:bg-[#4d7352]"
                            : "bg-zinc-400 hover:bg-zinc-500"
                    )}
                >
                    {isSpeaking ? (
                        <Volume2 className="h-6 w-6 text-white" />
                    ) : isListening ? (
                        <MicOff className="h-6 w-6 text-white" />
                    ) : (
                        <Mic className="h-6 w-6 text-white" />
                    )}
                </Button>

                {/* Status indicator */}
                {isConnected && (
                    <div className="absolute -top-1 -right-1 h-4 w-4 bg-green-500 rounded-full border-2 border-white"></div>
                )}
            </div>

            {/* Controls */}
            <div className="flex items-center gap-2">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleMute}
                    className="h-8 w-8 p-0"
                >
                    {isMuted ? (
                        <VolumeX className="h-4 w-4" />
                    ) : (
                        <Volume2 className="h-4 w-4" />
                    )}
                </Button>

                {isConnected && (
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={stopConversation}
                        className="text-xs px-2"
                    >
                        Disconnect
                    </Button>
                )}
            </div>

            {/* Status */}
            <div className="text-center">
                <p className={cn(
                    "text-sm font-medium",
                    isConnected ? "text-green-600" : "text-zinc-500"
                )}>
                    {isConnected ? "Connected" : "Disconnected"}
                </p>

                {isListening && (
                    <p className="text-xs text-blue-600 animate-pulse">
                        Listening...
                    </p>
                )}

                {isSpeaking && (
                    <p className="text-xs text-purple-600 animate-pulse">
                        Speaking...
                    </p>
                )}

                {error && (
                    <p className="text-xs text-red-600 mt-1">
                        {error}
                    </p>
                )}
            </div>

            {/* Conversation Display */}
            {conversationMessages.length > 0 && (
                <div className="w-full max-w-md max-h-32 overflow-y-auto bg-zinc-50 dark:bg-zinc-800 rounded-lg p-3">
                    <div className="space-y-2">
                        {conversationMessages.slice(-3).map((message, index) => (
                            <div
                                key={index}
                                className={cn(
                                    "text-xs p-2 rounded",
                                    message.role === "user"
                                        ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 ml-4"
                                        : "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 mr-4"
                                )}
                            >
                                <span className="font-medium">
                                    {message.role === "user" ? "You" : "Assistant"}:
                                </span>{" "}
                                {message.content}
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
}
