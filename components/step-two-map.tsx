"use client";

import { cn } from "@/lib/utils";
import { useState, useEffect } from "react";
import PropertyMap from "./property-map";
import { Button } from "@/components/ui/button";
import ConversationalVoiceAssistant from "./conversational-voice-assistant";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    DoorOpen,
    Check,
    ArrowLeft,
    Info
} from "lucide-react";

interface StepTwoMapData {
    fences: Array<{ points: Array<[number, number]>; length: number }>;
    gates: Array<{ position: [number, number]; width: number }>;
}

interface StepTwoMapProps extends React.HTMLAttributes<HTMLDivElement> {
    propertyAddress: string;
    data?: StepTwoMapData;
    onDataChange: (data: StepTwoMapData) => void;
    onNext: () => void;
    onBack: () => void;
    gateWidth?: number;
}

export default function StepTwoMap({
    propertyAddress,
    data = { fences: [], gates: [] },
    onDataChange,
    onNext,
    onBack,
    gateWidth = 3.6,
    className,
    ...props
}: StepTwoMapProps) {
    const [mapData, setMapData] = useState<StepTwoMapData>(data);
    const [mapLoaded, setMapLoaded] = useState(false);
    const [drawingInstructions, setDrawingInstructions] = useState<string>(
        "Click on the map to start drawing your fence line"
    );
    const [conversationMessages, setConversationMessages] = useState<any[]>([]);

    // Agent ID for the fencing quote assistant
    const AGENT_ID = "agent_01jx9e9wnsejt8z0tae0z7wf89";

    useEffect(() => {
        // Update drawing instructions based on current state
        if (mapData.fences.length === 0 && mapData.gates.length === 0) {
            setDrawingInstructions("Click on the map to start drawing your fence line");
        } else if (mapData.fences.length > 0 && mapData.gates.length === 0) {
            setDrawingInstructions("Great! Now click where you want to place gates");
        } else if (mapData.fences.length > 0 && mapData.gates.length > 0) {
            setDrawingInstructions("Perfect! You can continue drawing more fence lines or gates, or proceed to the next step");
        }
    }, [mapData]);

    const handleDrawingComplete = (drawingData: StepTwoMapData) => {
        setMapData(drawingData);
        onDataChange(drawingData);
    };

    // Handle field updates from conversational AI (for map guidance)
    const handleFieldUpdate = (field: string, value: string) => {
        // Handle map-specific commands from the AI
        if (field === "fence_design" && value === "complete") {
            if (isMapDataValid()) {
                onNext();
            }
        }
    };

    // Handle conversation updates
    const handleConversationUpdate = (messages: any[]) => {
        setConversationMessages(messages);
    };

    // Handle step completion from AI
    const handleStepComplete = () => {
        if (isMapDataValid()) {
            onNext();
        }
    };

    const getTotalFenceLength = () => {
        return mapData.fences.reduce((total, fence) => total + fence.length, 0);
    };

    const isMapDataValid = () => {
        return mapData.fences.length > 0; // At least one fence line is required
    };

    const clearAllDrawings = () => {
        const emptyData = { fences: [], gates: [] };
        setMapData(emptyData);
        onDataChange(emptyData);
    };

    return (
        <div
            className={cn(
                "w-full max-w-6xl mx-auto",
                "bg-white dark:bg-zinc-900",
                "border border-zinc-200 dark:border-zinc-800",
                "rounded-2xl overflow-hidden",
                "shadow-xl shadow-black/5",
                className
            )}
            {...props}
        >
            {/* Header */}
            <div className="px-8 pt-8 pb-6">
                <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold text-zinc-900 dark:text-white mb-2">
                        Design Your Fence Layout
                    </h2>
                    <p className="text-zinc-600 dark:text-zinc-400">
                        Draw fence lines and place gates on your property map
                    </p>
                </div>

                {/* Property Address Display */}
                <div className="flex items-center justify-center gap-2 mb-6 p-3 bg-zinc-50 dark:bg-zinc-800/50 rounded-lg">
                    <MapPin className="w-4 h-4 text-[#5d8c62]" />
                    <span className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                        {propertyAddress}
                    </span>
                </div>

                {/* Instructions */}
                <div className="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg mb-6">
                    <Info className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                    <div>
                        <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                            How to use the map:
                        </h3>
                        <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                            <li>• Click "Draw Fence" and click points on the map to create fence lines</li>
                            <li>• Click "Place Gate" and click on the map to add gate locations</li>
                            <li>• Double-click to finish drawing a fence line</li>
                            <li>• You can draw multiple fence sections and place multiple gates</li>
                        </ul>
                    </div>
                </div>

                {/* Current Status */}
                <div className="text-center p-3 bg-zinc-50 dark:bg-zinc-800/50 rounded-lg mb-6">
                    <p className="text-sm text-zinc-600 dark:text-zinc-400">
                        {drawingInstructions}
                    </p>
                </div>

                {/* Conversational Voice Assistant for Map Guidance */}
                <div className="flex flex-col items-center gap-4 mb-6 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                    <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
                        Map Design Assistant
                    </h3>
                    <p className="text-sm text-blue-800 dark:text-blue-200 text-center mb-4">
                        Get guidance on drawing your fence lines and placing gates. Ask questions about measurements, placement, or design decisions.
                    </p>

                    <ConversationalVoiceAssistant
                        agentId={AGENT_ID}
                        onFieldUpdate={handleFieldUpdate}
                        onStepComplete={handleStepComplete}
                        onConversationUpdate={handleConversationUpdate}
                        currentStep="design"
                        className="w-full"
                    />
                </div>
            </div>

            {/* Map Section */}
            <div className="px-8 pb-6">
                <div className="border border-zinc-200 dark:border-zinc-700 rounded-xl overflow-hidden">
                    {propertyAddress ? (
                        <PropertyMap
                            address={propertyAddress}
                            onMapLoaded={setMapLoaded}
                            onDrawingComplete={handleDrawingComplete}
                            gateWidth={gateWidth}
                        />
                    ) : (
                        <div className="h-[500px] flex items-center justify-center bg-zinc-50 dark:bg-zinc-800/50">
                            <div className="text-center">
                                <MapPin className="h-12 w-12 text-zinc-400 mx-auto mb-4" />
                                <p className="text-zinc-500 dark:text-zinc-400">
                                    Property address is required to load the map
                                </p>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Summary Section */}
            {(mapData.fences.length > 0 || mapData.gates.length > 0) && (
                <div className="px-8 pb-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-zinc-50 dark:bg-zinc-800/50 rounded-lg">
                        <div className="text-center">
                            <div className="flex items-center justify-center gap-2 mb-2">
                                <Fence className="w-5 h-5 text-[#5d8c62]" />
                                <span className="font-medium text-zinc-900 dark:text-zinc-100">
                                    Fence Lines
                                </span>
                            </div>
                            <p className="text-2xl font-bold text-[#5d8c62]">
                                {mapData.fences.length}
                            </p>
                            <p className="text-sm text-zinc-500">
                                {getTotalFenceLength().toFixed(1)}m total
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="flex items-center justify-center gap-2 mb-2">
                                <DoorOpen className="w-5 h-5 text-[#d9b38c]" />
                                <span className="font-medium text-zinc-900 dark:text-zinc-100">
                                    Gates
                                </span>
                            </div>
                            <p className="text-2xl font-bold text-[#d9b38c]">
                                {mapData.gates.length}
                            </p>
                            <p className="text-sm text-zinc-500">
                                {gateWidth}m width each
                            </p>
                        </div>

                        <div className="text-center">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={clearAllDrawings}
                                className="text-red-600 border-red-200 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                            >
                                Clear All
                            </Button>
                        </div>
                    </div>
                </div>
            )}

            {/* Action Buttons */}
            <div className="px-8 pb-8">
                <div className="flex items-center gap-4">
                    <Button
                        variant="outline"
                        onClick={onBack}
                        className="flex items-center gap-2 px-6 h-12"
                    >
                        <ArrowLeft className="w-4 h-4" />
                        Back to Details
                    </Button>

                    <Button
                        onClick={onNext}
                        disabled={!isMapDataValid()}
                        className={cn(
                            "flex-1 h-12 rounded-lg font-medium transition-all",
                            isMapDataValid()
                                ? "bg-[#5d8c62] hover:bg-[#4d7352] text-white shadow-md hover:shadow-lg"
                                : "bg-zinc-200 dark:bg-zinc-700 text-zinc-400 dark:text-zinc-500 cursor-not-allowed"
                        )}
                    >
                        {isMapDataValid() ? (
                            <>
                                <Check className="w-4 h-4 mr-2" />
                                Continue to Fence Options
                            </>
                        ) : (
                            "Please draw at least one fence line"
                        )}
                    </Button>
                </div>
            </div>
        </div>
    );
}
