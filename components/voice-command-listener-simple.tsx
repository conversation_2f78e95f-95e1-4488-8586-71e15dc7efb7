"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2 } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { ToastAction } from "@/components/ui/toast"

interface VoiceCommandListenerSimpleProps {
  onCommand: (command: string) => void
  isActive: boolean
  setIsActive: (active: boolean) => void
}

export default function VoiceCommandListenerSimple({
  onCommand,
  isActive,
  setIsActive,
}: VoiceCommandListenerSimpleProps) {
  const [isProcessing, setIsProcessing] = useState(false)

  // Toggle listening state
  const toggleListening = () => {
    setIsActive(!isActive)
  }

  // Simulate voice commands for testing
  useEffect(() => {
    if (!isActive) return

    let timeoutId: NodeJS.Timeout

    const simulateCommand = () => {
      setIsProcessing(true)

      // Wait for 2 seconds to simulate processing
      setTimeout(() => {
        setIsProcessing(false)

        // List of possible commands
        const commands = ["next", "previous", "step1", "step2", "step3", "step4", "submit"]

        // Select a random command
        const randomCommand = commands[Math.floor(Math.random() * commands.length)]

        // Show toast with recognized command
        toast({
          title: "Voice Command Recognized (Demo)",
          description: `"${randomCommand}"`,
          action: <ToastAction altText="OK">OK</ToastAction>,
        })

        // Execute the command
        onCommand(randomCommand)

        // Schedule next command if still active
        if (isActive) {
          timeoutId = setTimeout(simulateCommand, 10000) // Every 10 seconds
        }
      }, 2000)
    }

    // Start the simulation
    timeoutId = setTimeout(simulateCommand, 5000) // First command after 5 seconds

    return () => {
      clearTimeout(timeoutId)
    }
  }, [isActive, onCommand])

  return (
    <div className="relative">
      <Button
        type="button"
        size="sm"
        variant={isActive ? "default" : "outline"}
        onClick={toggleListening}
        className={`relative ${isActive ? "bg-[#5d8c62] hover:bg-[#4d7352]" : ""}`}
      >
        {isProcessing ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            <span>Processing...</span>
          </>
        ) : isActive ? (
          <>
            <MicOff className="h-4 w-4 mr-2" />
            <span>Voice Commands Active (Demo)</span>
            <span className="absolute -top-1 -right-1 flex h-3 w-3">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
              <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
            </span>
          </>
        ) : (
          <>
            <Mic className="h-4 w-4 mr-2" />
            <span>Enable Voice Commands (Demo)</span>
          </>
        )}
      </Button>
    </div>
  )
}
