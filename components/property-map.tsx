"use client"

import { useRef, useEffect, useState } from "react"
import { geocodeAddress, loadGoogleMapsScript } from "@/utils/maps-utils"
import { Button } from "@/components/ui/button"
import type { google } from "google-maps"

// Define types for Google Maps objects
declare global {
  interface Window {
    google: typeof google
  }
}

interface PropertyMapProps {
  address: string
  onMapLoaded: (loaded: boolean) => void
  onDrawingComplete: (data: {
    fences: Array<{ points: Array<[number, number]>; length: number }>
    gates: Array<{ position: [number, number]; width: number }>
  }) => void
  gateWidth: number
}

export default function PropertyMap({ address, onMapLoaded, onDrawingComplete, gateWidth }: PropertyMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const [map, setMap] = useState<google.maps.Map | null>(null)
  const [drawingManager, setDrawingManager] = useState<google.maps.drawing.DrawingManager | null>(null)
  const [drawingMode, setDrawingMode] = useState<"fence" | "gate" | null>(null)
  const [drawnItems, setDrawnItems] = useState<{
    fences: Array<{ points: Array<[number, number]>; length: number; polyline: google.maps.Polyline }>
    gates: Array<{ position: [number, number]; width: number; marker: google.maps.Marker }>
  }>({
    fences: [],
    gates: [],
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Get Google Maps API key from environment variable
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || ""

  // Debug logging
  useEffect(() => {
    console.log("PropertyMap Debug:", {
      address,
      apiKey: apiKey ? `${apiKey.substring(0, 10)}...` : "missing",
      hasApiKey: !!apiKey
    })
  }, [address, apiKey])

  // Function to check and convert a point to [lat, lng] format
  const checkAndConvertPoint = (point: google.maps.LatLng | null): [number, number] | null => {
    if (!point) return null
    return [point.lat(), point.lng()]
  }

  // Initialize map when component mounts
  useEffect(() => {
    if (!address || !mapRef.current || !apiKey) {
      setError("Please provide a valid address and API key")
      onMapLoaded(false)
      return
    }

    const initMap = async () => {
      try {
        setLoading(true)
        console.log("Starting map initialization...")

        // Load Google Maps API with drawing library
        console.log("Loading Google Maps script...")
        await loadGoogleMapsScript(apiKey)
        console.log("Google Maps script loaded successfully")

        // Geocode the address to get coordinates
        console.log("Geocoding address:", address)
        const coordinates = await geocodeAddress(address, apiKey)
        console.log("Geocoding result:", coordinates)

        if (!coordinates) {
          console.error("Failed to geocode address")
          setError("Google Maps API unavailable. Please enable billing on your Google Cloud Project or enter coordinates manually.")
          onMapLoaded(false)
          setLoading(false)
          return
        }

        // Create the map using browser Google Maps API
        const mapInstance = new window.google.maps.Map(mapRef.current, {
          center: coordinates,
          zoom: 18,
          mapTypeId: window.google.maps.MapTypeId.SATELLITE,
          disableDefaultUI: true,
          zoomControl: true,
        })

        // Create drawing manager
        const drawingManagerInstance = new window.google.maps.drawing.DrawingManager({
          drawingMode: null,
          drawingControl: false,
          polylineOptions: {
            strokeColor: "#5d8c62",
            strokeWeight: 4,
            editable: false,
          },
        })

        drawingManagerInstance.setMap(mapInstance)

        setMap(mapInstance)
        setDrawingManager(drawingManagerInstance)
        setLoading(false)
        onMapLoaded(true)

        // Add click listener for placing gates
        const clickListener = mapInstance.addListener("click", (event) => {
          if (drawingMode === "gate") {
            placeGate(event.latLng!)
          }
        })

        // Add polyline complete listener for fences
        const polylineCompleteListener = window.google.maps.event.addListener(
          drawingManagerInstance,
          "polylinecomplete",
          (polyline) => {
            if (drawingMode === "fence") {
              saveFence(polyline)
            }
          },
        )

        return () => {
          // Clean up listeners
          window.google.maps.event.removeListener(clickListener)
          window.google.maps.event.removeListener(polylineCompleteListener)
        }
      } catch (err) {
        console.error("Error initializing map:", err)
        setError("Failed to load map. Please try again.")
        onMapLoaded(false)
        setLoading(false)
      }
    }

    initMap()
  }, [address, apiKey]) // Remove onMapLoaded and drawingMode from dependencies

  // Update drawing mode
  useEffect(() => {
    if (!drawingManager) return

    if (drawingMode === "fence") {
      drawingManager.setDrawingMode(window.google.maps.drawing.OverlayType.POLYLINE)
    } else {
      drawingManager.setDrawingMode(null)
    }
  }, [drawingMode, drawingManager])

  // Update parent component with drawn items
  useEffect(() => {
    const simplifiedItems = {
      fences: drawnItems.fences.map((fence) => ({
        points: fence.points,
        length: fence.length,
      })),
      gates: drawnItems.gates.map((gate) => ({
        position: gate.position,
        width: gate.width,
      })),
    }

    onDrawingComplete(simplifiedItems)
  }, [drawnItems]) // Remove onDrawingComplete from dependencies

  // Place a gate marker on the map
  const placeGate = (latLng: google.maps.LatLng) => {
    if (!map) return

    const marker = new window.google.maps.Marker({
      position: latLng,
      map: map,
      icon: {
        path: window.google.maps.SymbolPath.CIRCLE,
        scale: 12,
        fillColor: "#d9b38c",
        fillOpacity: 1,
        strokeColor: "#8b5a3c",
        strokeWeight: 2,
      },
      label: {
        text: "G",
        color: "white",
        fontSize: "12px",
        fontWeight: "bold",
      },
      title: "Gate Position"
    })

    // Convert the LatLng to [lat, lng] format
    const position: [number, number] = [latLng.lat(), latLng.lng()]

    setDrawnItems((prev) => ({
      ...prev,
      gates: [...prev.gates, { position, width: gateWidth, marker }],
    }))
  }

  // Save a fence polyline
  const saveFence = (polyline: google.maps.Polyline) => {
    const path = polyline.getPath()
    const points: Array<[number, number]> = []
    let length = 0

    // Calculate length and collect points
    for (let i = 0; i < path.getLength(); i++) {
      const point = path.getAt(i)
      // Convert the LatLng to [lat, lng] format directly
      const convertedPoint: [number, number] = [point.lat(), point.lng()]
      points.push(convertedPoint)

      if (i > 0) {
        const prevPoint = path.getAt(i - 1)
        length += window.google.maps.geometry.spherical.computeDistanceBetween(prevPoint, point)
      }
    }

    setDrawnItems((prev) => ({
      ...prev,
      fences: [...prev.fences, { points, length, polyline }],
    }))

    // Reset drawing mode after completing a fence
    setDrawingMode(null)
  }

  // Clear all drawn items
  const clearDrawings = () => {
    // Remove all polylines and markers from the map
    drawnItems.fences.forEach((fence) => fence.polyline.setMap(null))
    drawnItems.gates.forEach((gate) => gate.marker.setMap(null))

    // Reset drawn items
    setDrawnItems({
      fences: [],
      gates: [],
    })
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-2 mb-4">
        <Button
          variant={drawingMode === "fence" ? "default" : "outline"}
          onClick={() => setDrawingMode("fence")}
          className={drawingMode === "fence" ? "bg-[#5d8c62]" : ""}
          size="sm"
          disabled={loading}
        >
          <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
          </svg>
          Draw Fence
        </Button>
        <Button
          variant={drawingMode === "gate" ? "default" : "outline"}
          onClick={() => setDrawingMode("gate")}
          className={drawingMode === "gate" ? "bg-[#d9b38c]" : ""}
          size="sm"
          disabled={loading}
        >
          <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
            />
          </svg>
          Place Gate
        </Button>
        <Button variant="outline" onClick={clearDrawings} size="sm" disabled={loading}>
          <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            />
          </svg>
          Clear All
        </Button>
      </div>

      <div className="relative border rounded-lg overflow-hidden">
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75 z-10">
            <div className="text-center">
              <svg
                className="animate-spin h-8 w-8 text-[#5d8c62] mx-auto mb-4"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <p>Loading property map...</p>
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-red-50 z-10">
            <div className="text-center p-4">
              <svg className="h-8 w-8 text-red-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
              <p className="text-red-600">{error}</p>
              <p className="mt-2 text-sm text-gray-600">Please check the address and try again.</p>
            </div>
          </div>
        )}

        <div
          ref={mapRef}
          className="w-full h-[400px]"
          aria-label="Property map for drawing fence lines and placing gates"
        ></div>

        <div className="absolute bottom-4 right-4 bg-white p-2 rounded-lg shadow-md text-sm">
          <div className="flex items-center mb-1">
            <div className="w-4 h-1 bg-[#5d8c62] mr-2"></div>
            <span>Fence Line</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-[#d9b38c] mr-2"></div>
            <span>Gate</span>
          </div>
        </div>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-yellow-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">Drawing Instructions</h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>1. Select "Draw Fence" and click to create fence lines</p>
              <p>2. Select "Place Gate" and click to position gates</p>
              <p>3. Use "Clear All" to start over if needed</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
