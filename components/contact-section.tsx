import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

export default function ContactSection() {
  return (
    <section id="contact" className="py-24 px-6 bg-[#f8f8f8]">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start mb-16">
          <h2 className="text-3xl font-light">CONTACT US</h2>
          <p className="max-w-xl mt-4 md:mt-0 text-gray-600">
            Get in touch with our team to discuss your timber fencing requirements or to schedule a consultation.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div>
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Name
                  </label>
                  <Input id="name" placeholder="Your name" />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <Input id="email" type="email" placeholder="Your email" />
                </div>
              </div>
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                  Phone
                </label>
                <Input id="phone" placeholder="Your phone number" />
              </div>
              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                  Subject
                </label>
                <Input id="subject" placeholder="What is this regarding?" />
              </div>
              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                  Message
                </label>
                <Textarea id="message" placeholder="Your message" rows={5} />
              </div>
              <Button className="bg-[#5d8c62] hover:bg-[#4d7352] text-white w-full">SEND MESSAGE</Button>
            </form>
          </div>

          <div className="flex flex-col justify-between h-full">
            <div>
              <h3 className="text-xl font-medium mb-6">Contact Information</h3>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <div className="h-6 w-6 mr-3 mt-0.5 flex-shrink-0 text-[#5d8c62]">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium">Address</h4>
                    <p className="text-gray-600 mt-1">267 Sutherlands Rd, Riddells Creek VIC 3431, Australia</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="h-6 w-6 mr-3 mt-0.5 flex-shrink-0 text-[#5d8c62]">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium">Phone</h4>
                    <p className="text-gray-600 mt-1">0468 701 149</p>
                    <p className="text-gray-600">0497 397 948</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="h-6 w-6 mr-3 mt-0.5 flex-shrink-0 text-[#5d8c62]">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium">Email</h4>
                    <p className="text-gray-600 mt-1"><EMAIL></p>
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="h-6 w-6 mr-3 mt-0.5 flex-shrink-0 text-[#5d8c62]">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium">Business Hours</h4>
                    <p className="text-gray-600 mt-1">Opens 8am Tuesday</p>
                    <p className="text-gray-600">Please call for specific hours</p>
                  </div>
                </li>
              </ul>
            </div>

            <div className="mt-12 p-6 bg-white rounded-lg shadow-md">
              <h3 className="text-lg font-medium mb-4">Request a Site Visit</h3>
              <p className="text-gray-600 mb-6">
                For accurate quotes and personalized advice, we offer free on-site consultations throughout the Macedon
                Ranges area.
              </p>
              <Button className="bg-[#5d8c62] hover:bg-[#4d7352] text-white w-full">SCHEDULE A CONSULTATION</Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
