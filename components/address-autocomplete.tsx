"use client"

import { useRef, useEffect, useState } from "react"
import { Input } from "@/components/ui/input"
import { loadGoogleMapsScript } from "@/utils/maps-utils"

interface AddressAutocompleteProps {
  id: string
  name: string
  placeholder: string
  value: string
  onChange: (value: string) => void
  required?: boolean
  className?: string
}

export default function AddressAutocomplete({
  id,
  name,
  placeholder,
  value,
  onChange,
  required,
  className
}: AddressAutocompleteProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const initAutocomplete = async () => {
      try {
        console.log("🔍 [DEBUG] AddressAutocomplete: Starting initialization...");
        const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
        console.log("🔍 [DEBUG] AddressAutocomplete: API key present:", !!apiKey);

        if (!apiKey) {
          console.error("❌ [DEBUG] AddressAutocomplete: No API key found - using fallback mode");
          setIsLoaded(true); // Enable fallback mode
          return;
        }

        // Load Google Maps with Places library
        console.log("🔍 [DEBUG] AddressAutocomplete: Loading Google Maps script...");
        await loadGoogleMapsScript(apiKey)
        console.log("✅ [DEBUG] AddressAutocomplete: Google Maps script loaded");

        console.log("🔍 [DEBUG] AddressAutocomplete: Checking Google Maps availability...");
        console.log("🔍 [DEBUG] AddressAutocomplete: window.google exists:", !!window.google);
        console.log("🔍 [DEBUG] AddressAutocomplete: window.google.maps exists:", !!window.google?.maps);
        console.log("🔍 [DEBUG] AddressAutocomplete: window.google.maps.places exists:", !!window.google?.maps?.places);
        console.log("🔍 [DEBUG] AddressAutocomplete: Autocomplete constructor exists:", !!window.google?.maps?.places?.Autocomplete);

        if (inputRef.current && window.google?.maps?.places?.Autocomplete) {
          console.log("🔍 [DEBUG] AddressAutocomplete: Creating autocomplete instance...");

          // Create autocomplete instance
          autocompleteRef.current = new window.google.maps.places.Autocomplete(inputRef.current, {
            types: ['address'],
            componentRestrictions: { country: 'au' }, // Restrict to Australia
            fields: ['formatted_address', 'geometry']
          })
          console.log("✅ [DEBUG] AddressAutocomplete: Autocomplete instance created");

          // Listen for place selection
          autocompleteRef.current.addListener('place_changed', () => {
            console.log("🔍 [DEBUG] AddressAutocomplete: Place changed event fired");
            const place = autocompleteRef.current?.getPlace()
            console.log("🔍 [DEBUG] AddressAutocomplete: Selected place:", place);
            if (place?.formatted_address) {
              console.log("✅ [DEBUG] AddressAutocomplete: Updating address:", place.formatted_address);
              onChange(place.formatted_address)
            }
          })

          setIsLoaded(true)
          console.log("✅ [DEBUG] AddressAutocomplete: Initialization complete");
        } else {
          console.error("❌ [DEBUG] AddressAutocomplete: Missing required components:", {
            inputRef: !!inputRef.current,
            google: !!window.google,
            maps: !!window.google?.maps,
            places: !!window.google?.maps?.places,
            Autocomplete: !!window.google?.maps?.places?.Autocomplete
          });
        }
      } catch (error) {
        console.error('❌ [DEBUG] AddressAutocomplete: Error initializing autocomplete:', error)
        console.log('🔍 [DEBUG] AddressAutocomplete: Falling back to manual input mode');
        setIsLoaded(true); // Enable fallback mode even if Google Maps fails
      }
    }

    initAutocomplete()

    // Cleanup
    return () => {
      if (autocompleteRef.current) {
        console.log("🔍 [DEBUG] AddressAutocomplete: Cleaning up autocomplete instance");
        window.google?.maps?.event?.clearInstanceListeners(autocompleteRef.current)
      }
    }
  }, [onChange])

  // Handle manual input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value)
  }

  return (
    <Input
      ref={inputRef}
      id={id}
      name={name}
      placeholder={isLoaded && !autocompleteRef.current ? `${placeholder} (manual entry)` : placeholder}
      value={value}
      onChange={handleInputChange}
      required={required}
      className={className}
      title={isLoaded && !autocompleteRef.current ? "Google Maps autocomplete unavailable - please enter address manually" : undefined}
    />
  )
}