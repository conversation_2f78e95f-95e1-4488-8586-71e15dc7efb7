"use client"

import { useRef, useEffect, useState } from "react"
import { Input } from "@/components/ui/input"
import { loadGoogleMapsScript } from "@/utils/maps-utils"

interface AddressAutocompleteProps {
  id: string
  name: string
  placeholder: string
  value: string
  onChange: (value: string) => void
  required?: boolean
  className?: string
}

export default function AddressAutocomplete({
  id,
  name,
  placeholder,
  value,
  onChange,
  required,
  className
}: AddressAutocompleteProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const initAutocomplete = async () => {
      try {
        const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
        if (!apiKey) return

        // Load Google Maps with Places library
        await loadGoogleMapsScript(apiKey)
        
        if (inputRef.current && window.google?.maps?.places) {
          // Create autocomplete instance
          autocompleteRef.current = new window.google.maps.places.Autocomplete(inputRef.current, {
            types: ['address'],
            componentRestrictions: { country: 'au' }, // Restrict to Australia
            fields: ['formatted_address', 'geometry']
          })

          // Listen for place selection
          autocompleteRef.current.addListener('place_changed', () => {
            const place = autocompleteRef.current?.getPlace()
            if (place?.formatted_address) {
              onChange(place.formatted_address)
            }
          })

          setIsLoaded(true)
        }
      } catch (error) {
        console.error('Error initializing autocomplete:', error)
      }
    }

    initAutocomplete()

    // Cleanup
    return () => {
      if (autocompleteRef.current) {
        window.google?.maps?.event?.clearInstanceListeners(autocompleteRef.current)
      }
    }
  }, [onChange])

  // Handle manual input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value)
  }

  return (
    <Input
      ref={inputRef}
      id={id}
      name={name}
      placeholder={placeholder}
      value={value}
      onChange={handleInputChange}
      required={required}
      className={className}
    />
  )
}