# Quote Wizard Components

A modern, voice-enabled quote wizard for collecting customer information and designing fence layouts on Google Maps.

## Components Overview

### 1. `StepOneForm` - Contact & Address Collection
- **Purpose**: Collects customer contact information and property address
- **Features**:
  - Voice input with real-time audio visualization
  - Form validation with visual feedback
  - Google Places autocomplete for addresses
  - Responsive design with dark mode support

### 2. `StepTwoMap` - Fence Design on Google Maps
- **Purpose**: Interactive map for drawing fence lines and placing gates
- **Features**:
  - Google Maps integration with drawing tools
  - Real-time fence length calculation
  - Gate placement with customizable width
  - Visual summary of drawn elements

### 3. `QuoteWizard` - Main Orchestrator
- **Purpose**: Manages the multi-step workflow
- **Features**:
  - Progress indicator
  - Data persistence between steps
  - Toast notifications for user feedback
  - Completion callback with all collected data

## Usage

### Basic Implementation

```tsx
import QuoteWizard from "@/components/quote-wizard";

export default function MyPage() {
  const handleComplete = (data) => {
    console.log("Quote data:", data);
    // Process the collected data
    // Navigate to next step (fence options, quote generation, etc.)
  };

  return (
    <QuoteWizard
      onComplete={handleComplete}
      gateWidth={3.6} // Optional: default gate width in meters
    />
  );
}
```

### Data Structure

The wizard returns data in this format:

```typescript
interface QuoteWizardData {
  stepOne: {
    name: string;
    email: string;
    phone: string;
    propertyAddress: string;
    datePlanned: string;
  };
  stepTwo: {
    fences: Array<{
      points: Array<[number, number]>; // [lat, lng] coordinates
      length: number; // in meters
    }>;
    gates: Array<{
      position: [number, number]; // [lat, lng] coordinates
      width: number; // in meters
    }>;
  };
}
```

## Voice Input Features

### Step 1 Voice Input
- Users can speak their contact information
- Supports natural language: "My name is John Smith, email <EMAIL>, phone 0412345678"
- Real-time audio visualization during recording
- Automatic form field population

### Voice Input API Integration
- Uses `/api/transcribe-form` endpoint
- Requires ElevenLabs or Groq API keys for transcription
- Graceful fallback with error messages when APIs unavailable

## Google Maps Integration

### Requirements
- `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY` environment variable
- Google Maps JavaScript API enabled
- Google Places API enabled (for address autocomplete)

### Map Features
- Satellite view of property
- Drawing tools for fence lines
- Click-to-place gates
- Real-time distance calculation
- Clear all drawings functionality

## Styling & Theming

### Design System
- Uses Tailwind CSS with custom color scheme
- Primary color: `#5d8c62` (green)
- Secondary color: `#d9b38c` (brown/tan)
- Supports dark mode automatically

### Responsive Design
- Mobile-first approach
- Adaptive layouts for different screen sizes
- Touch-friendly controls for mobile devices

## Integration with Existing Quote System

### Replacing Current Step 1
To integrate with the existing quote system in `/app/quote/page.tsx`:

1. Replace the current step 1 form with `StepOneForm`
2. Replace the current map section with `StepTwoMap`
3. Use the wizard data to populate existing form state

### Example Integration

```tsx
// In your existing quote page
import StepOneForm from "@/components/step-one-form";
import StepTwoMap from "@/components/step-two-map";

// Replace step 1 content
{step === 1 && (
  <StepOneForm
    data={formData}
    onDataChange={(data) => setFormData(prev => ({ ...prev, ...data }))}
    onNext={() => setStep(2)}
    onVoiceInput={handleFormVoiceInput}
  />
)}

// Replace step 2 content  
{step === 2 && (
  <StepTwoMap
    propertyAddress={formData.propertyAddress}
    data={{ fences: drawnItems.fences, gates: drawnItems.gates }}
    onDataChange={(data) => setDrawnItems(data)}
    onNext={() => setStep(3)}
    onBack={() => setStep(1)}
    gateWidth={formData.gateWidth}
  />
)}
```

## Testing

### Demo Page
Visit `/quote-wizard` to test the complete wizard flow.

### Voice Input Testing
1. Ensure microphone permissions are granted
2. Configure API keys for transcription services
3. Test with various speech patterns and accents

### Map Testing
1. Verify Google Maps API key is configured
2. Test address autocomplete functionality
3. Test fence drawing and gate placement
4. Verify distance calculations are accurate

## Dependencies

### Required Packages
- `@/components/ui/*` - UI components (Button, Input, etc.)
- `@/utils/maps-utils` - Google Maps utilities
- `@/lib/utils` - Utility functions (cn, etc.)
- `lucide-react` - Icons
- `react` - Core React functionality

### API Dependencies
- Google Maps JavaScript API
- Google Places API
- ElevenLabs API (for voice transcription)
- Groq API (fallback for voice transcription)

## Environment Variables

```env
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
ELEVENLABS_API_KEY=your_elevenlabs_api_key
GROQ_API_KEY=your_groq_api_key
```

## Browser Support

### Voice Features
- Chrome/Edge: Full support
- Firefox: Full support
- Safari: Limited support (no MediaRecorder in older versions)
- Mobile browsers: Varies by device and OS

### Map Features
- All modern browsers with JavaScript enabled
- Requires internet connection for Google Maps

## Performance Considerations

### Voice Input
- Audio chunks are processed in real-time
- Large audio files may impact performance
- Consider implementing audio compression for production

### Maps
- Maps are loaded on-demand when address is provided
- Drawing data is stored in memory (consider persistence for large projects)
- Consider implementing map tile caching for offline scenarios

## Security Considerations

### Voice Data
- Audio data is sent to third-party APIs (ElevenLabs/Groq)
- Consider implementing client-side transcription for sensitive data
- Ensure HTTPS is used for all API calls

### Maps Data
- Property addresses and coordinates are sensitive
- Consider data encryption for stored fence layouts
- Implement proper access controls for saved designs
