# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Next.js 15 application for Macedon Ranges Country Timber Gates & Fencing that features an AI-powered quote system with voice command capabilities. The application allows users to design custom fence and gate layouts on an interactive map and receive instant quotes.

## Development Commands

```bash
# Start the development server
npm run dev

# Build for production
npm run build

# Start the production server
npm run start

# Run linting
npm run lint
```

## Architecture Overview

### Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: TailwindCSS, Radix UI, shadcn/ui components
- **AI Integration**: GROQ API for text generation and speech processing
- **Mapping**: Google Maps API for property visualization and fence design
- **Form Handling**: react-hook-form with zod validation

### Core Features

1. **AI-Powered Quote Generation**: Instant pricing calculation with AI-generated personalized responses
2. **Voice Command System**: Natural language interface for navigating and completing forms
3. **Interactive Property Map**: Visual design tool for drawing fence lines and gate placement
4. **Multi-Step Form**: Guided process for collecting customer information and property details

## Voice Command System

The voice command system consists of several components that work together:

### Components

- **VoiceCommandListener**: Processes navigation commands (next, previous, go to step X)
- **FloatingVoiceAssistant**: Main voice interface that handles multi-field extraction
- **FormVoiceAssistant**: Modal-based voice input for filling form sections
- **VoiceInput**: Field-specific voice input for individual form fields

### API Routes

- **/api/transcribe-command**: Processes audio for navigation commands
- **/api/transcribe**: Transcribes audio for specific form fields
- **/api/transcribe-form**: Multi-field extraction from conversational input
- **/api/text-to-speech**: Generates spoken feedback for system responses

### Data Flow

1. Audio is captured from the microphone using MediaRecorder API
2. Audio data is sent to the appropriate API endpoint
3. GROQ AI service transcribes audio to text
4. System extracts commands or form field data using regex and AI processing
5. UI updates based on extracted information
6. Audio/visual feedback confirms successful processing

## Quote Generation Flow

The quote generation process uses a multi-step approach:

### Step 1: Customer Details

Collects contact information with support for voice input to fill multiple fields at once.

### Step 2: Site Details

Features an interactive Google Maps component that allows users to:
- Search for their property address
- Draw fence lines with polylines
- Place gates with markers
- Automatically calculate fence length and gate dimensions

### Step 3: Fence & Gate Details

Customization options including:
- Fence style (post and rail, solid, picket)
- Gate style (country swing, decorative, security)
- Timber type (treated pine, jarrah, ironbark)
- Automation options

### Step 4: Quote Result

Displays a comprehensive quote with:
- Real-time price calculation based on measurements and selections
- Breakdown of materials, labor, and delivery costs
- AI-generated personalized message
- Options to request changes or confirm

### Pricing Logic

Prices are calculated in real-time using base rates and multipliers for:
- Fence type and length
- Gate style and width
- Timber selection
- Automation options

## Key Files

- **/app/quote/page.tsx**: Main quote generation workflow
- **/components/property-map.tsx**: Interactive map for fence/gate design
- **/components/voice-command-listener.tsx**: Voice navigation system
- **/components/floating-voice-assistant.tsx**: Main voice interface
- **/app/api/transcribe-command/route.ts**: Navigation command processing
- **/app/api/transcribe-form/route.ts**: Multi-field extraction logic
- **/utils/maps-utils.ts**: Helper functions for Google Maps integration
- **/utils/audio-utils.ts**: Helper functions for audio processing

## Development Guidelines

### Working with the Voice System

- Voice command responses should be concise and match the expected command patterns
- When modifying voice commands, update the help dialog in voice-command-help.tsx
- The system prompt in the transcription endpoints guides AI interpretation

### Map Integration

- The PropertyMap component requires Google Maps API credentials
- Fence line drawing uses the Google Maps drawing library
- Distance calculations convert pixel measurements to real-world meters

### Quote Calculation

- Pricing logic is defined in the calculateRealtimePrice function
- Each fence/gate type has specific base rates and multipliers
- When modifying pricing, update both the calculation function and AI prompt