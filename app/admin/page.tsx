"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export default function AdminDashboard() {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  // Mock data for dashboard
  const stats = [
    { title: "Total Quotes", value: "124", change: "+12%", changeType: "positive" },
    { title: "Conversion Rate", value: "38%", change: "+5%", changeType: "positive" },
    { title: "Avg. Quote Value", value: "$8,450", change: "+$420", changeType: "positive" },
    { title: "Pending Approvals", value: "7", change: "-2", changeType: "negative" },
  ]

  const recentQuotes = [
    {
      id: "Q-2023-089",
      customer: "<PERSON>",
      date: "2023-05-14",
      amount: "$9,850",
      status: "pending",
      type: "Post and Rail",
      location: "Woodend",
    },
    {
      id: "Q-2023-088",
      customer: "Sarah Johnson",
      date: "2023-05-13",
      amount: "$7,230",
      status: "approved",
      type: "Picket Fence",
      location: "Kyneton",
    },
    {
      id: "Q-2023-087",
      customer: "Michael Brown",
      date: "2023-05-12",
      amount: "$12,450",
      status: "paid",
      type: "Privacy Fence",
      location: "Gisborne",
    },
    {
      id: "Q-2023-086",
      customer: "Emma Wilson",
      date: "2023-05-11",
      amount: "$5,980",
      status: "installed",
      type: "Decorative Fence",
      location: "Macedon",
    },
    {
      id: "Q-2023-085",
      customer: "David Thompson",
      date: "2023-05-10",
      amount: "$8,740",
      status: "review",
      type: "Post and Rail",
      location: "Trentham",
    },
  ]

  const upcomingInstallations = [
    {
      id: "I-2023-042",
      customer: "Sarah Johnson",
      date: "2023-05-20",
      location: "Kyneton",
      status: "scheduled",
      crew: "Team A",
    },
    {
      id: "I-2023-043",
      customer: "Michael Brown",
      date: "2023-05-22",
      location: "Gisborne",
      status: "confirmed",
      crew: "Team B",
    },
    {
      id: "I-2023-044",
      customer: "Jennifer Davis",
      date: "2023-05-25",
      location: "Woodend",
      status: "pending",
      crew: "Team A",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Sidebar */}
      <aside className={`sidebar ${sidebarOpen ? "sidebar-open" : ""} fixed inset-y-0 left-0 z-50 bg-white shadow-md`}>
        <div className="flex h-16 items-center justify-between px-4 border-b">
          <div className="flex items-center space-x-2">
            <div className="h-8 w-auto">
              <img
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/96061AC2-C754-4A06-8BE6-79441715A1A1_4_5005_c-jVQ0rJnxWKJ24LglltxlMeDQTnPOD8.jpeg"
                alt="Macedon Ranges Country Timber Gates & Fencing Logo"
                className="h-full w-auto"
              />
            </div>
            <span
              className={`font-medium transition-opacity duration-200 ${sidebarOpen ? "opacity-100" : "opacity-0 lg:opacity-100"}`}
            >
              ADMIN DASHBOARD
            </span>
          </div>
          <button className="lg:hidden" onClick={() => setSidebarOpen(false)}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-4">
          <div className="flex items-center space-x-3 mb-8">
            <Avatar>
              <AvatarImage src="/woman-profile.png" alt="Lisa" />
              <AvatarFallback>LA</AvatarFallback>
            </Avatar>
            <div
              className={`transition-opacity duration-200 ${sidebarOpen ? "opacity-100" : "opacity-0 lg:opacity-100"}`}
            >
              <p className="font-medium">Lisa Anderson</p>
              <p className="text-sm text-gray-500">Administrator</p>
            </div>
          </div>

          <nav className="space-y-1">
            <a href="#" className="nav-item active flex items-center px-3 py-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                />
              </svg>
              <span
                className={`transition-opacity duration-200 ${sidebarOpen ? "opacity-100" : "opacity-0 lg:opacity-100"}`}
              >
                Dashboard
              </span>
            </a>
            <a href="#" className="nav-item flex items-center px-3 py-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                />
              </svg>
              <span
                className={`transition-opacity duration-200 ${sidebarOpen ? "opacity-100" : "opacity-0 lg:opacity-100"}`}
              >
                Quotes
              </span>
            </a>
            <a href="#" className="nav-item flex items-center px-3 py-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              <span
                className={`transition-opacity duration-200 ${sidebarOpen ? "opacity-100" : "opacity-0 lg:opacity-100"}`}
              >
                Schedule
              </span>
            </a>
            <a href="#" className="nav-item flex items-center px-3 py-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              <span
                className={`transition-opacity duration-200 ${sidebarOpen ? "opacity-100" : "opacity-0 lg:opacity-100"}`}
              >
                Customers
              </span>
            </a>
            <a href="#" className="nav-item flex items-center px-3 py-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
              <span
                className={`transition-opacity duration-200 ${sidebarOpen ? "opacity-100" : "opacity-0 lg:opacity-100"}`}
              >
                Reports
              </span>
            </a>
            <a href="#" className="nav-item flex items-center px-3 py-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
              <span
                className={`transition-opacity duration-200 ${sidebarOpen ? "opacity-100" : "opacity-0 lg:opacity-100"}`}
              >
                Settings
              </span>
            </a>
          </nav>
        </div>

        <div className="absolute bottom-0 left-0 right-0 p-4">
          <Link href="/">
            <Button variant="outline" className="w-full justify-start">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-3"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              <span
                className={`transition-opacity duration-200 ${sidebarOpen ? "opacity-100" : "opacity-0 lg:opacity-100"}`}
              >
                Back to Website
              </span>
            </Button>
          </Link>
        </div>
      </aside>

      {/* Main Content */}
      <div className={`main-content ${sidebarOpen ? "" : "main-content-expanded"} lg:main-content`}>
        {/* Top Navigation */}
        <header className="bg-white shadow-sm h-16 flex items-center px-4">
          <button className="lg:hidden mr-4" onClick={() => setSidebarOpen(true)}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>

          <div className="flex-1">
            <h1 className="text-xl font-medium">Dashboard</h1>
          </div>

          <div className="flex items-center space-x-4">
            <button className="relative p-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                />
              </svg>
              <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></span>
            </button>

            <div className="h-8 border-l border-gray-300"></div>

            <div className="flex items-center space-x-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/woman-profile.png" alt="Lisa" />
                <AvatarFallback>LA</AvatarFallback>
              </Avatar>
              <span className="font-medium text-sm">Lisa</span>
            </div>
          </div>
        </header>

        {/* Dashboard Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <div className="flex justify-between items-center">
                    <p className="text-sm font-medium text-gray-500">{stat.title}</p>
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        stat.changeType === "positive" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                      }`}
                    >
                      {stat.change}
                    </span>
                  </div>
                  <p className="text-3xl font-bold mt-2">{stat.value}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Recent Quotes</CardTitle>
                <CardDescription>Review and manage recent quote requests</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">ID</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Customer</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Type</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Location</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Amount</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Status</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentQuotes.map((quote, index) => (
                        <tr key={index} className="quote-row border-b">
                          <td className="py-3 px-4 text-sm">{quote.id}</td>
                          <td className="py-3 px-4 text-sm">{quote.customer}</td>
                          <td className="py-3 px-4 text-sm">{quote.type}</td>
                          <td className="py-3 px-4 text-sm">{quote.location}</td>
                          <td className="py-3 px-4 text-sm font-medium">{quote.amount}</td>
                          <td className="py-3 px-4 text-sm">
                            <Badge
                              className={`
                              ${quote.status === "pending" ? "bg-yellow-100 text-yellow-800 hover:bg-yellow-100" : ""}
                              ${quote.status === "approved" ? "bg-blue-100 text-blue-800 hover:bg-blue-100" : ""}
                              ${quote.status === "paid" ? "bg-green-100 text-green-800 hover:bg-green-100" : ""}
                              ${quote.status === "installed" ? "bg-gray-100 text-gray-800 hover:bg-gray-100" : ""}
                              ${quote.status === "review" ? "bg-red-100 text-red-800 hover:bg-red-100" : ""}
                            `}
                            >
                              {quote.status.charAt(0).toUpperCase() + quote.status.slice(1)}
                            </Badge>
                          </td>
                          <td className="py-3 px-4 text-sm">
                            <Button variant="outline" size="sm">
                              View
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="mt-4 text-center">
                  <Button variant="outline" size="sm">
                    View All Quotes
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Upcoming Installations</CardTitle>
                <CardDescription>Next 7 days schedule</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingInstallations.map((installation, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium">{installation.customer}</p>
                          <p className="text-sm text-gray-500">{installation.location}</p>
                        </div>
                        <Badge
                          className={`
                          ${installation.status === "scheduled" ? "bg-green-100 text-green-800 hover:bg-green-100" : ""}
                          ${installation.status === "confirmed" ? "bg-blue-100 text-blue-800 hover:bg-blue-100" : ""}
                          ${installation.status === "pending" ? "bg-yellow-100 text-yellow-800 hover:bg-yellow-100" : ""}
                        `}
                        >
                          {installation.status.charAt(0).toUpperCase() + installation.status.slice(1)}
                        </Badge>
                      </div>
                      <div className="mt-2 flex justify-between items-center text-sm">
                        <div className="flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-gray-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                          <span>{installation.date}</span>
                        </div>
                        <div className="flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-gray-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                            />
                          </svg>
                          <span>{installation.crew}</span>
                        </div>
                      </div>
                      <div className="mt-3 flex space-x-2">
                        <Button variant="outline" size="sm" className="text-xs">
                          Details
                        </Button>
                        <Button variant="outline" size="sm" className="text-xs">
                          Reschedule
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4 text-center">
                  <Button variant="outline" size="sm">
                    View Full Schedule
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Quote Analytics</CardTitle>
                <CardDescription>Quote trends over the last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="quotes">
                  <TabsList className="mb-4">
                    <TabsTrigger value="quotes">Quotes</TabsTrigger>
                    <TabsTrigger value="conversion">Conversion</TabsTrigger>
                    <TabsTrigger value="value">Value</TabsTrigger>
                  </TabsList>
                  <TabsContent value="quotes" className="h-[250px] flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <p>Chart would display here showing quote volume trends</p>
                    </div>
                  </TabsContent>
                  <TabsContent value="conversion" className="h-[250px] flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <p>Chart would display here showing quote conversion rates</p>
                    </div>
                  </TabsContent>
                  <TabsContent value="value" className="h-[250px] flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <p>Chart would display here showing quote value trends</p>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>AI Quote Insights</CardTitle>
                <CardDescription>Performance metrics for the AI quoting system</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-500">Quotes Generated</p>
                    <p className="text-2xl font-bold">87</p>
                    <p className="text-xs text-green-600">+23% from last month</p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-500">Conversion Rate</p>
                    <p className="text-2xl font-bold">42%</p>
                    <p className="text-xs text-green-600">+8% from last month</p>
                  </div>
                </div>

                <h4 className="font-medium mb-3">Popular Fence Types</h4>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Post and Rail</span>
                      <span className="text-sm font-medium">48%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-[#5d8c62] h-2 rounded-full" style={{ width: "48%" }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Picket Fence</span>
                      <span className="text-sm font-medium">27%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-[#5d8c62] h-2 rounded-full" style={{ width: "27%" }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Privacy Fence</span>
                      <span className="text-sm font-medium">18%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-[#5d8c62] h-2 rounded-full" style={{ width: "18%" }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Decorative Fence</span>
                      <span className="text-sm font-medium">7%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-[#5d8c62] h-2 rounded-full" style={{ width: "7%" }}></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
