import Link from "next/link"
import HeroSection from "@/components/hero-section"
import ServicesSection from "@/components/services-section"
import ProjectsGallery from "@/components/projects-gallery"
import TestimonialsSection from "@/components/testimonials-section"
import QuotePreview from "@/components/quote-preview"
import ContactSection from "@/components/contact-section"
import { Button } from "@/components/ui/button"

export default function Home() {
  return (
    <div className="min-h-screen bg-[#f8f8f8]">
      <header className="flex items-center justify-between p-6 bg-white">
        <div className="flex items-center space-x-3">
          <div className="h-16 w-auto">
            <img
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/96061AC2-C754-4A06-8BE6-79441715A1A1_4_5005_c-jVQ0rJnxWKJ24LglltxlMeDQTnPOD8.jpeg"
              alt="Macedon Ranges Country Timber Gates & Fencing Logo"
              className="h-full w-auto"
            />
          </div>
        </div>
        <div className="hidden md:flex items-center space-x-6">
          <Link href="/" className="text-sm hover:underline">
            HOME
          </Link>
          <Link href="/about" className="text-sm hover:underline">
            ABOUT
          </Link>
          <Link href="/gates" className="text-sm hover:underline">
            GATES
          </Link>
          <Link href="/timber-fencing" className="text-sm hover:underline">
            TIMBER FENCING
          </Link>
          <Link href="/horse-fencing" className="text-sm hover:underline">
            HORSE FENCING
          </Link>
          <Link href="/gallery" className="text-sm hover:underline">
            GALLERY
          </Link>
          <Link href="/faq" className="text-sm hover:underline">
            FAQ
          </Link>
          <Link href="/#contact" className="text-sm hover:underline">
            CONTACT
          </Link>
        </div>
        <Link href="/quote-wizard" className="hidden md:block">
          <Button className="bg-[#8bc283] hover:bg-[#7ab176] text-white rounded-md">GET A QUOTE</Button>
        </Link>
        <button className="flex md:hidden flex-col space-y-1">
          <span className="h-0.5 w-6 bg-black"></span>
          <span className="h-0.5 w-6 bg-black"></span>
        </button>
      </header>

      <main>
        <HeroSection />
        <ServicesSection />
        <ProjectsGallery />
        <QuotePreview />
        <TestimonialsSection />
        <ContactSection />
      </main>

      <footer className="bg-[#3c2a1e] text-white py-12 px-6">
        <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-lg font-medium mb-4">Macedon Ranges Country Timber Gates & Fencing</h3>
            <p className="text-sm text-gray-300 mb-4">
              Custom, show-stopping fences and gates that keep your property secure and elevate your property's style.
            </p>
            <div className="flex space-x-4">
              <a
                href="https://www.facebook.com/MacedonRangesCountryTimberGatesFencing"
                className="text-gray-300 hover:text-white"
              >
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path
                    fillRule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clipRule="evenodd"
                  />
                </svg>
              </a>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-medium mb-4">Quick Links</h3>
            <ul className="space-y-2 text-sm text-gray-300">
              <li>
                <Link href="/#services" className="hover:text-white">
                  Services
                </Link>
              </li>
              <li>
                <Link href="/#projects" className="hover:text-white">
                  Projects
                </Link>
              </li>
              <li>
                <Link href="/#testimonials" className="hover:text-white">
                  Testimonials
                </Link>
              </li>
              <li>
                <Link href="/quote-wizard" className="hover:text-white">
                  Get a Quote
                </Link>
              </li>
              <li>
                <Link href="/#contact" className="hover:text-white">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-medium mb-4">Contact Information</h3>
            <ul className="space-y-2 text-sm text-gray-300">
              <li className="flex items-start">
                <svg
                  className="h-5 w-5 mr-2 mt-0.5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  ></path>
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  ></path>
                </svg>
                <span>267 Sutherlands Rd, Riddells Creek VIC 3431, Australia</span>
              </li>
              <li className="flex items-start">
                <svg
                  className="h-5 w-5 mr-2 mt-0.5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                  ></path>
                </svg>
                <span>0468 701 149 | 0497 397 948</span>
              </li>
              <li className="flex items-start">
                <svg
                  className="h-5 w-5 mr-2 mt-0.5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  ></path>
                </svg>
                <span><EMAIL></span>
              </li>
              <li className="flex items-start">
                <svg
                  className="h-5 w-5 mr-2 mt-0.5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>Opens 8am Tuesday</span>
              </li>
            </ul>
          </div>
        </div>
        <div className="max-w-7xl mx-auto mt-8 pt-8 border-t border-gray-700 text-center text-sm text-gray-400">
          <p>
            © {new Date().getFullYear()} Macedon Ranges Country Timber Gates & Fencing Pty Ltd. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}
