"use client";

import { useState, useEffect } from "react";
import QuoteWizard from "@/components/quote-wizard";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface QuoteWizardData {
    stepOne: {
        name: string;
        email: string;
        phone: string;
        propertyAddress: string;
        datePlanned: string;
    };
    stepTwo: {
        fences: Array<{ points: Array<[number, number]>; length: number }>;
        gates: Array<{ position: [number, number]; width: number }>;
    };
}

export default function QuoteWizardPage() {
    const [isComplete, setIsComplete] = useState(false);
    const [quoteData, setQuoteData] = useState<QuoteWizardData | null>(null);
    const [debugLogs, setDebugLogs] = useState<string[]>([]);
    const [showDebug, setShowDebug] = useState(true);

    // Capture console logs for debugging
    useEffect(() => {
        const originalLog = console.log;
        const originalError = console.error;

        console.log = (...args) => {
            const message = args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');

            if (message.includes('[DEBUG]')) {
                setDebugLogs(prev => [...prev.slice(-20), `${new Date().toLocaleTimeString()}: ${message}`]);
            }
            originalLog(...args);
        };

        console.error = (...args) => {
            const message = args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');

            if (message.includes('[DEBUG]')) {
                setDebugLogs(prev => [...prev.slice(-20), `${new Date().toLocaleTimeString()}: ❌ ${message}`]);
            }
            originalError(...args);
        };

        return () => {
            console.log = originalLog;
            console.error = originalError;
        };
    }, []);

    const handleWizardComplete = (data: QuoteWizardData) => {
        setQuoteData(data);
        setIsComplete(true);
        console.log("Quote wizard completed with data:", data);
    };

    // Debug environment variables on component mount
    useEffect(() => {
        console.log("🔍 [DEBUG] Environment Variables Check:");
        console.log("🔍 [DEBUG] NEXT_PUBLIC_AGENT_ID:", process.env.NEXT_PUBLIC_AGENT_ID);
        console.log("🔍 [DEBUG] ELEVENLABS_API_KEY exists:", !!process.env.ELEVENLABS_API_KEY);
        console.log("🔍 [DEBUG] Current URL:", window.location.href);
    }, []);

    const handleStartOver = () => {
        setIsComplete(false);
        setQuoteData(null);
    };

    const getTotalFenceLength = () => {
        if (!quoteData) return 0;
        return quoteData.stepTwo.fences.reduce((total, fence) => total + fence.length, 0);
    };

    if (isComplete && quoteData) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-zinc-50 to-zinc-100 dark:from-zinc-900 dark:to-zinc-800 py-8 px-4">
                <div className="max-w-4xl mx-auto">
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-2xl text-center text-[#5d8c62]">
                                Conversational AI Quote Complete! 🎉
                            </CardTitle>
                            <CardDescription className="text-center">
                                Your fence design has been captured using our advanced conversational AI. Here's a summary of your project:
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Customer Details */}
                            <div>
                                <h3 className="text-lg font-semibold mb-3">Customer Details</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span className="font-medium">Name:</span> {quoteData.stepOne.name}
                                    </div>
                                    <div>
                                        <span className="font-medium">Email:</span> {quoteData.stepOne.email}
                                    </div>
                                    <div>
                                        <span className="font-medium">Phone:</span> {quoteData.stepOne.phone}
                                    </div>
                                    <div>
                                        <span className="font-medium">Planned Date:</span> {quoteData.stepOne.datePlanned || "Not specified"}
                                    </div>
                                </div>
                            </div>

                            {/* Property Details */}
                            <div>
                                <h3 className="text-lg font-semibold mb-3">Property Details</h3>
                                <div className="text-sm">
                                    <span className="font-medium">Address:</span> {quoteData.stepOne.propertyAddress}
                                </div>
                            </div>

                            {/* Fence Design */}
                            <div>
                                <h3 className="text-lg font-semibold mb-3">Fence Design</h3>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div className="text-center p-4 bg-[#5d8c62]/10 rounded-lg">
                                        <div className="text-2xl font-bold text-[#5d8c62]">
                                            {quoteData.stepTwo.fences.length}
                                        </div>
                                        <div className="text-sm text-zinc-600">Fence Lines</div>
                                    </div>
                                    <div className="text-center p-4 bg-[#d9b38c]/10 rounded-lg">
                                        <div className="text-2xl font-bold text-[#d9b38c]">
                                            {quoteData.stepTwo.gates.length}
                                        </div>
                                        <div className="text-sm text-zinc-600">Gates</div>
                                    </div>
                                    <div className="text-center p-4 bg-blue-500/10 rounded-lg">
                                        <div className="text-2xl font-bold text-blue-600">
                                            {getTotalFenceLength().toFixed(1)}m
                                        </div>
                                        <div className="text-sm text-zinc-600">Total Length</div>
                                    </div>
                                </div>
                            </div>

                            {/* Conversational AI Features */}
                            <div className="border-t pt-6">
                                <h3 className="text-lg font-semibold mb-3">🤖 Conversational AI Experience</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                                        <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">Natural Conversation</h4>
                                        <p className="text-sm text-green-700 dark:text-green-300">
                                            Used ElevenLabs Conversational AI 2.0 for natural, context-aware dialogue
                                        </p>
                                    </div>
                                    <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Real-time Voice</h4>
                                        <p className="text-sm text-blue-700 dark:text-blue-300">
                                            Real-time speech-to-text and text-to-speech with voice responses
                                        </p>
                                    </div>
                                    <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                                        <h4 className="font-medium text-purple-800 dark:text-purple-200 mb-2">Smart Field Extraction</h4>
                                        <p className="text-sm text-purple-700 dark:text-purple-300">
                                            Automatically extracted and populated form fields from natural speech
                                        </p>
                                    </div>
                                    <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                                        <h4 className="font-medium text-orange-800 dark:text-orange-200 mb-2">Design Guidance</h4>
                                        <p className="text-sm text-orange-700 dark:text-orange-300">
                                            Provided interactive guidance for map design and fence layout
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Next Steps */}
                            <div className="border-t pt-6">
                                <h3 className="text-lg font-semibold mb-3">What's Next?</h3>
                                <div className="space-y-2 text-sm text-zinc-600">
                                    <p>✅ Your contact details and property address have been captured via voice</p>
                                    <p>✅ Your fence layout has been designed with AI guidance</p>
                                    <p>🔄 Next: Choose fence materials, styles, and get your quote</p>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex gap-4 pt-4">
                                <Button
                                    onClick={handleStartOver}
                                    variant="outline"
                                    className="flex-1"
                                >
                                    Start Over
                                </Button>
                                <Button
                                    className="flex-1 bg-[#5d8c62] hover:bg-[#4d7352]"
                                    onClick={() => {
                                        // In a real app, this would navigate to the next step
                                        alert("This would proceed to fence options and quote generation!");
                                    }}
                                >
                                    Continue to Options
                                </Button>
                            </div>

                            {/* Debug Data */}
                            {process.env.NODE_ENV === "development" && (
                                <details className="mt-6">
                                    <summary className="cursor-pointer text-sm font-medium text-zinc-600">
                                        View Raw Data (Development)
                                    </summary>
                                    <pre className="mt-2 p-4 bg-zinc-100 dark:bg-zinc-800 rounded text-xs overflow-auto">
                                        {JSON.stringify(quoteData, null, 2)}
                                    </pre>
                                </details>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        );
    }

    return (
        <div className="relative">
            <QuoteWizard
                onComplete={handleWizardComplete}
                gateWidth={3.6}
            />

            {/* Debug Panel */}
            {showDebug && (
                <div className="fixed bottom-0 left-0 right-0 bg-black text-green-400 p-4 max-h-64 overflow-y-auto font-mono text-xs border-t-2 border-green-500 z-50">
                    <div className="flex justify-between items-center mb-2">
                        <h3 className="text-green-300 font-bold">🔍 Debug Console</h3>
                        <div className="flex gap-2">
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setDebugLogs([])}
                                className="text-xs h-6 px-2 bg-gray-800 text-green-400 border-green-600 hover:bg-gray-700"
                            >
                                Clear
                            </Button>
                            <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setShowDebug(false)}
                                className="text-xs h-6 px-2 bg-gray-800 text-green-400 border-green-600 hover:bg-gray-700"
                            >
                                Hide
                            </Button>
                        </div>
                    </div>
                    <div className="space-y-1">
                        {debugLogs.length === 0 ? (
                            <div className="text-gray-500">No debug logs yet. Click the microphone to start debugging...</div>
                        ) : (
                            debugLogs.map((log, index) => (
                                <div key={index} className="whitespace-pre-wrap break-all">
                                    {log}
                                </div>
                            ))
                        )}
                    </div>
                </div>
            )}

            {/* Show Debug Button when hidden */}
            {!showDebug && (
                <Button
                    onClick={() => setShowDebug(true)}
                    className="fixed bottom-4 right-4 bg-green-600 hover:bg-green-700 text-white z-50"
                    size="sm"
                >
                    Show Debug
                </Button>
            )}
        </div>
    );
}
