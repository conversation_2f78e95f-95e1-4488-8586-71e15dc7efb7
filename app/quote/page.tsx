"use client"

import type React from "react"

import { useState, useRef, useEffect, useCallback } from "react"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { generateText } from "ai"
import { groq } from "@ai-sdk/groq"
import PropertyMap from "@/components/property-map"
import AddressAutocomplete from "@/components/address-autocomplete"
import { Volume2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import FloatingVoiceAssistant from "@/components/floating-voice-assistant"

export default function QuotePage() {
  const { toast } = useToast()
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [quoteGenerated, setQuoteGenerated] = useState(false)
  const [aiResponse, setAiResponse] = useState("")
  const [aiThinking, setAiThinking] = useState(false)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [drawingMode, setDrawingMode] = useState<"fence" | "gate" | null>(null)
  const [drawnItems, setDrawnItems] = useState<{
    fences: Array<{ points: Array<[number, number]>; length: number }>
    gates: Array<{ position: [number, number]; width: number }>
  }>({
    fences: [],
    gates: [],
  })

  const canvasRef = useRef<HTMLCanvasElement>(null)
  const mapImageRef = useRef<HTMLImageElement | null>(null)
  const drawingRef = useRef<{
    isDrawing: boolean
    currentPoints: Array<[number, number]>
  }>({
    isDrawing: false,
    currentPoints: [],
  })

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    datePlanned: "",
    propertyAddress: "",
    projectType: "both",
    fenceType: "post-and-rail-3",
    fenceHeight: 1.2,
    fenceColor: "natural",
    timberType: "cypress",
    gateType: "country-swing",
    gateWidth: 3.6,
    gateHeight: 1.2,
    gateColor: "natural",
    automationRequired: false,
    automationType: "solar",
    additionalNotes: "",
  })

  // Calculated measurements from drawing
  const [calculatedMeasurements, setCalculatedMeasurements] = useState({
    totalFenceLength: 0,
    gateCount: 0,
    averageGateWidth: 3.6,
  })

  // Quote result
  const [quoteResult, setQuoteResult] = useState({
    materials: 0,
    labor: 0,
    delivery: 0,
    subtotal: 0,
    gst: 0,
    total: 0,
    estimatedDays: 0,
  })

  // Real-time price calculation
  const [realtimePrice, setRealtimePrice] = useState({
    fencePerMeter: 0,
    gatePrice: 0,
    automationPrice: 0,
    estimatedTotal: 0,
  })

  // Stable callback for drawing completion
  const handleDrawingComplete = useCallback((items: {
    fences: Array<{ points: Array<[number, number]>; length: number }>
    gates: Array<{ position: [number, number]; width: number }>
  }) => {
    setDrawnItems(items)
    setCalculatedMeasurements({
      totalFenceLength: items.fences.reduce((sum, fence) => sum + fence.length, 0),
      gateCount: items.gates.length,
      averageGateWidth: formData.gateWidth,
    })
  }, [formData.gateWidth])

  // Function to speak text using GROQ API
  const speakTextWithGroq = async (text: string) => {
    try {
      const response = await fetch("/api/text-to-speech", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ text }),
      })

      if (!response.ok) {
        console.error("Failed to convert text to speech")
        return
      }

      // In a real implementation, we would play the audio returned by the API
      const data = await response.json()
      console.log("Text to speech response:", data)
    } catch (error) {
      console.error("Error speaking text with GROQ:", error)
    }
  }

  useEffect(() => {
    // Calculate real-time price based on current selections
    const calculateRealtimePrice = () => {
      // Base rates
      const fenceBaseRates = {
        "post-and-rail-2": 95,
        "post-and-rail-3": 120,
        "post-and-rail-4": 145,
        "post-and-wire": 80,
        custom: 160,
      }

      const gateBaseRates = {
        "country-swing": 850,
        decorative: 950,
        security: 1200,
        custom: 1500,
      }

      const timberMultiplier = {
        cypress: 1,
        "treated-pine": 0.9,
        hardwood: 1.3,
        cedar: 1.2,
      }

      const automationPrices = {
        solar: 1800,
        "240v": 1500,
      }

      // Calculate fence price per meter
      const fenceType = formData.fenceType as keyof typeof fenceBaseRates
      const timberType = formData.timberType as keyof typeof timberMultiplier
      const fencePerMeter = fenceBaseRates[fenceType] * timberMultiplier[timberType]

      // Calculate gate price
      const gateType = formData.gateType as keyof typeof gateBaseRates
      const gateWidth = formData.gateWidth
      const gatePrice = gateBaseRates[gateType] * timberMultiplier[timberType] * (gateWidth / 3.6)

      // Calculate automation price if required
      const automationType = formData.automationType as keyof typeof automationPrices
      const automationPrice = formData.automationRequired ? automationPrices[automationType] : 0

      // Calculate estimated total
      let fenceLength = calculatedMeasurements.totalFenceLength || 50 // Default to 50m if no drawing
      let gateCount = calculatedMeasurements.gateCount || 1 // Default to 1 gate if no drawing

      if (formData.projectType === "gate") {
        fenceLength = 0
      } else if (formData.projectType === "fence") {
        gateCount = 0
      }

      const estimatedTotal = fenceLength * fencePerMeter + gateCount * gatePrice + automationPrice

      setRealtimePrice({
        fencePerMeter,
        gatePrice,
        automationPrice,
        estimatedTotal,
      })
    }

    calculateRealtimePrice()
  }, [formData, calculatedMeasurements])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }))
  }

  const handleVoiceInput = (fieldName: string, text: string) => {
    setFormData((prev) => ({ ...prev, [fieldName]: text }))
  }

  const handleFormVoiceInput = (fields: Record<string, string>) => {
    // Update form data with recognized fields
    setFormData((prev) => {
      const newFormData = { ...prev }

      // Process each recognized field
      Object.entries(fields).forEach(([key, value]) => {
        if (key in newFormData && value.trim()) {
          newFormData[key as keyof typeof newFormData] = value.trim()
        }
      })

      return newFormData
    })

    // Notify user of updated fields
    const fieldNames = Object.keys(fields)
      .filter((field) => fields[field]?.trim())
      .map((field) => field.replace(/([A-Z])/g, " $1").toLowerCase())

    if (fieldNames.length > 0) {
      toast({
        title: "Voice Input Recognized",
        description: `Updated: ${fieldNames.join(", ")}`,
        duration: 3000,
      })

      // Log the updates for debugging
      console.log("Form updated with voice input:", fields)
    }
  }

  const nextStep = () => {
    if (step === 1) {
      // Copy address to propertyAddress if it's empty
      if (!formData.propertyAddress) {
        setFormData((prev) => ({ ...prev, propertyAddress: prev.address }))
      }

      // Reset map loaded state
      setMapLoaded(false)
    }

    setStep((prev) => prev + 1)
    window.scrollTo(0, 0)

    // Announce step change
    speakTextWithGroq(`Moving to step ${step + 1}`)
  }

  const prevStep = () => {
    setStep((prev) => prev - 1)
    window.scrollTo(0, 0)

    // Announce step change
    speakTextWithGroq(`Moving to step ${step - 1}`)
  }

  const confirmDrawing = () => {
    // Update form data with calculated measurements
    setFormData((prev) => ({
      ...prev,
      // If we have gates, set project type to include gates
      projectType:
        calculatedMeasurements.gateCount > 0
          ? calculatedMeasurements.totalFenceLength > 0
            ? "both"
            : "gate"
          : "fence",
    }))

    // Move to next step
    nextStep()
  }

  const generateQuote = async () => {
    setLoading(true)
    setAiThinking(true)

    try {
      // Calculate quote based on real-time price
      const materials = Math.round(realtimePrice.estimatedTotal * 0.7) // 70% of total for materials
      const labor = Math.round(realtimePrice.estimatedTotal * 0.25) // 25% of total for labor
      const delivery = Math.round(realtimePrice.estimatedTotal * 0.05) // 5% of total for delivery
      const subtotal = materials + labor + delivery
      const gst = Math.round(subtotal * 0.1)
      const total = subtotal + gst

      // Estimate installation days based on fence length and gate count
      const fenceDays = Math.ceil(calculatedMeasurements.totalFenceLength / 20)
      const gateDays = calculatedMeasurements.gateCount
      const estimatedDays = Math.max(1, fenceDays + gateDays)

      setQuoteResult({
        materials,
        labor,
        delivery,
        subtotal,
        gst,
        total,
        estimatedDays,
      })

      // Generate AI response
      const prompt = `
        You are an AI assistant for Macedon Ranges Country Timber Gates & Fencing. A customer has requested a quote with the following details:
        
        Name: ${formData.name}
        Property Address: ${formData.propertyAddress}
        Project Type: ${formData.projectType === "both" ? "Fence and Gate" : formData.projectType === "fence" ? "Fence Only" : "Gate Only"}
        ${formData.projectType !== "gate" ? `Fence Type: ${formData.fenceType.replace(/-/g, " ")}` : ""}
        ${formData.projectType !== "gate" ? `Fence Length: ${calculatedMeasurements.totalFenceLength.toFixed(1)} meters` : ""}
        ${formData.projectType !== "fence" ? `Gate Type: ${formData.gateType.replace(/-/g, " ")}` : ""}
        ${formData.projectType !== "fence" ? `Gate Count: ${calculatedMeasurements.gateCount}` : ""}
        ${formData.projectType !== "fence" && formData.automationRequired ? `Gate Automation: ${formData.automationType}` : ""}
        Timber Type: ${formData.timberType.replace(/-/g, " ")}
        Additional Notes: ${formData.additionalNotes}
        
        The quote has been calculated as:
        Materials: $${materials.toLocaleString()}
        Labor: $${labor.toLocaleString()}
        Delivery: $${delivery.toLocaleString()}
        GST: $${gst.toLocaleString()}
        Total: $${total.toLocaleString()}
        Estimated Installation Time: ${estimatedDays} days
        
        Please write a friendly, professional message to the customer explaining their quote. Include:
        1. A thank you for their interest
        2. A brief explanation of what's included in the quote
        3. Next steps (mention that Lisa will review the quote and get back to them with a confirmed price)
        4. Mention that all quotes are subject to a final site inspection
        Keep it concise but warm and professional.
      `

      const { text } = await generateText({
        model: groq("llama3-70b-8192"),
        prompt: prompt,
      })

      setAiResponse(text)
      setQuoteGenerated(true)
      setAiThinking(false)
    } catch (error) {
      console.error("Error generating quote:", error)
      setAiResponse(
        "We apologize, but there was an error generating your quote. Please try again or contact us directly.",
      )
      setAiThinking(false)
    } finally {
      setLoading(false)
    }
  }

  // Get required fields based on current step
  const getRequiredFieldsForStep = () => {
    switch (step) {
      case 1:
        return ["name", "email", "phone"]
      case 2:
        return []
      case 3:
        return []
      default:
        return []
    }
  }

  return (
    <div className="min-h-screen bg-[#f8f8f8]">
      <header className="flex items-center justify-between p-6 bg-white shadow-sm">
        <Link href="/" className="flex items-center space-x-3">
          <div className="h-10 w-auto">
            <img
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/96061AC2-C754-4A06-8BE6-79441715A1A1_4_5005_c-jVQ0rJnxWKJ24LglltxlMeDQTnPOD8.jpeg"
              alt="Macedon Ranges Country Timber Gates & Fencing Logo"
              className="h-full w-auto"
            />
          </div>
          <span className="font-medium">MRCT GATES & FENCING</span>
        </Link>

        <div className="flex items-center space-x-2">
          <Link href="/" className="text-sm hover:underline ml-4">
            ← Back to Home
          </Link>
        </div>
      </header>

      <main className="max-w-5xl mx-auto py-12 px-6 pb-24">
        <div className="mb-12 text-center">
          <h1 className="text-3xl font-light mb-4">AI-POWERED FENCING QUOTE</h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Get an instant quote for your timber fencing project. Our AI system will analyze your requirements and
            provide you with a detailed estimate.
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-12">
          <div className="flex items-center justify-between">
            <div className={`flex flex-col items-center ${step >= 1 ? "text-[#5d8c62]" : "text-gray-400"}`}>
              <div
                className={`h-10 w-10 rounded-full flex items-center justify-center border-2 ${step >= 1 ? "border-[#5d8c62] bg-[#5d8c62]/10" : "border-gray-300"}`}
              >
                1
              </div>
              <span className="text-sm mt-2">Details & Site</span>
            </div>
            <div className={`h-1 flex-1 mx-4 ${step >= 2 ? "bg-[#5d8c62]" : "bg-gray-300"}`}></div>
            <div className={`flex flex-col items-center ${step >= 2 ? "text-[#5d8c62]" : "text-gray-400"}`}>
              <div
                className={`h-10 w-10 rounded-full flex items-center justify-center border-2 ${step >= 2 ? "border-[#5d8c62] bg-[#5d8c62]/10" : "border-gray-300"}`}
              >
                2
              </div>
              <span className="text-sm mt-2">Fence Details</span>
            </div>
            <div className={`h-1 flex-1 mx-4 ${step >= 3 ? "bg-[#5d8c62]" : "bg-gray-300"}`}></div>
            <div className={`flex flex-col items-center ${step >= 3 ? "text-[#5d8c62]" : "text-gray-400"}`}>
              <div
                className={`h-10 w-10 rounded-full flex items-center justify-center border-2 ${step >= 3 ? "border-[#5d8c62] bg-[#5d8c62]/10" : "border-gray-300"}`}
              >
                3
              </div>
              <span className="text-sm mt-2">Your Quote</span>
            </div>
          </div>
        </div>

        {/* Voice Input Info */}
        <div className="mb-8 bg-[#5d8c62]/10 border border-[#5d8c62]/20 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0 mt-1">
              <Volume2 className="h-5 w-5 text-[#5d8c62]" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-[#5d8c62]">Voice Assistant Available</h3>
              <div className="mt-1 text-sm text-gray-600">
                <p>
                  Use the floating voice assistant button at the bottom right of the screen to fill in the form by
                  speaking.
                </p>
                <p className="mt-1">
                  Just tap the button and speak naturally. You'll see your words appear in real-time, and the assistant
                  will automatically fill in your information.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Step 1: Customer Details & Site Details */}
        {step === 1 && (
          <Card>
            <CardHeader>
              <CardTitle>Your Details & Property Site</CardTitle>
              <CardDescription>Please provide your contact information and draw your fence lines on the map.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Customer Details Section */}
              <div className="space-y-6">
                <h3 className="text-lg font-medium border-b pb-2">Contact Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="John Smith"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      name="phone"
                      placeholder="0412 345 678"
                      value={formData.phone}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="datePlanned">Date Planned</Label>
                    <Input
                      id="datePlanned"
                      name="datePlanned"
                      type="date"
                      value={formData.datePlanned}
                      onChange={(e) => {
                        // Only allow valid date formats for date input
                        const value = e.target.value
                        if (value === '' || /^\d{4}-\d{2}-\d{2}$/.test(value)) {
                          setFormData(prev => ({ ...prev, datePlanned: value }))
                        }
                      }}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="propertyAddress">Property Address</Label>
                  <AddressAutocomplete
                    id="propertyAddress"
                    name="propertyAddress"
                    placeholder="456 Farm Rd, Kyneton VIC"
                    value={formData.propertyAddress}
                    onChange={(value) => setFormData(prev => ({ ...prev, propertyAddress: value }))}
                  />
                  <p className="text-xs text-gray-500">Where the fencing work will be done</p>
                </div>
              </div>

              {/* Property Map Section */}
              <div className="space-y-6">
                <h3 className="text-lg font-medium border-b pb-2">Property Map & Design</h3>
                <div className="bg-white p-4 rounded-lg border">
                  {/* Debug info */}
                  <div className="text-xs text-gray-400 mb-2">
                    Debug: propertyAddress = "{formData.propertyAddress}" (length: {formData.propertyAddress.length})
                  </div>
                  
                  {formData.propertyAddress ? (
                    <>
                      <p className="text-sm text-gray-600 mb-4">{formData.propertyAddress}</p>
                      
                      <PropertyMap
                        address={formData.propertyAddress}
                        onMapLoaded={setMapLoaded}
                        onDrawingComplete={handleDrawingComplete}
                        gateWidth={formData.gateWidth}
                      />
                    </>
                  ) : (
                    <div className="h-[400px] flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                      <div className="text-center">
                        <svg className="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <p className="text-gray-500">Enter a property address above to load the map</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={nextStep} className="bg-[#5d8c62] hover:bg-[#4d7352]">
                Next Step
              </Button>
            </CardFooter>
          </Card>
        )}

        {/* Step 2: Fence Details */}
        {step === 2 && (
          <Card>
            <CardHeader>
              <CardTitle>Fence & Gate Details</CardTitle>
              <CardDescription>Select your preferred options for your fencing project.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Real-time price calculator */}
              <div className="bg-white p-4 rounded-lg border sticky top-4 z-10 shadow-md">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Estimated Price</h3>
                  <div className="text-2xl font-bold text-[#5d8c62]">
                    ${Math.round(realtimePrice.estimatedTotal).toLocaleString()}
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">Price updates automatically as you select options</div>
              </div>

              {/* Project Type Selection */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Project Type</h3>
                <div className="flex flex-wrap gap-3">
                  <Badge
                    className={`px-4 py-2 cursor-pointer ${formData.projectType === "fence" ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                    onClick={() => handleSelectChange("projectType", "fence")}
                  >
                    Fence Only
                  </Badge>
                  <Badge
                    className={`px-4 py-2 cursor-pointer ${formData.projectType === "gate" ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                    onClick={() => handleSelectChange("projectType", "gate")}
                  >
                    Gate Only
                  </Badge>
                  <Badge
                    className={`px-4 py-2 cursor-pointer ${formData.projectType === "both" ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                    onClick={() => handleSelectChange("projectType", "both")}
                  >
                    Both Fence & Gate
                  </Badge>
                </div>
              </div>

              {/* Fence Options - Show if project type is fence or both */}
              {(formData.projectType === "fence" || formData.projectType === "both") && (
                <div className="space-y-6 border-t pt-6">
                  <h3 className="text-lg font-medium">Fence Options</h3>

                  <div className="space-y-4">
                    <Label>Fence Type</Label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${formData.fenceType === "post-and-rail-2" ? "border-[#5d8c62] bg-[#5d8c62]/5 ring-1 ring-[#5d8c62]" : "hover:border-gray-300"}`}
                        onClick={() => handleSelectChange("fenceType", "post-and-rail-2")}
                      >
                        <div className="h-24 bg-gray-100 rounded-md mb-3 flex items-center justify-center">
                          <Image
                            src="/two-rail-fence.png"
                            alt="2-Rail Post and Rail"
                            width={150}
                            height={100}
                            className="rounded-md"
                          />
                        </div>
                        <h4 className="font-medium">2-Rail Post and Rail</h4>
                        <p className="text-sm text-gray-500 mt-1">Simple, economical design</p>
                      </div>

                      <div
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${formData.fenceType === "post-and-rail-3" ? "border-[#5d8c62] bg-[#5d8c62]/5 ring-1 ring-[#5d8c62]" : "hover:border-gray-300"}`}
                        onClick={() => handleSelectChange("fenceType", "post-and-rail-3")}
                      >
                        <div className="h-24 bg-gray-100 rounded-md mb-3 flex items-center justify-center">
                          <Image
                            src="/rural-timber-fence.png"
                            alt="3-Rail Post and Rail"
                            width={150}
                            height={100}
                            className="rounded-md"
                          />
                        </div>
                        <h4 className="font-medium">3-Rail Post and Rail</h4>
                        <p className="text-sm text-gray-500 mt-1">Most popular option</p>
                      </div>

                      <div
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${formData.fenceType === "post-and-rail-4" ? "border-[#5d8c62] bg-[#5d8c62]/5 ring-1 ring-[#5d8c62]" : "hover:border-gray-300"}`}
                        onClick={() => handleSelectChange("fenceType", "post-and-rail-4")}
                      >
                        <div className="h-24 bg-gray-100 rounded-md mb-3 flex items-center justify-center">
                          <Image
                            src="/four-rail-fence.png"
                            alt="4-Rail Post and Rail"
                            width={150}
                            height={100}
                            className="rounded-md"
                          />
                        </div>
                        <h4 className="font-medium">4-Rail Post and Rail</h4>
                        <p className="text-sm text-gray-500 mt-1">Extra security and strength</p>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <Label>Fence Height</Label>
                      <div className="flex flex-wrap gap-3">
                        <Badge
                          className={`px-4 py-2 cursor-pointer ${formData.fenceHeight === 1.2 ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                          onClick={() => handleSelectChange("fenceHeight", "1.2")}
                        >
                          1.2m (Standard)
                        </Badge>
                        <Badge
                          className={`px-4 py-2 cursor-pointer ${formData.fenceHeight === 1.5 ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                          onClick={() => handleSelectChange("fenceHeight", "1.5")}
                        >
                          1.5m (Medium)
                        </Badge>
                        <Badge
                          className={`px-4 py-2 cursor-pointer ${formData.fenceHeight === 1.8 ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                          onClick={() => handleSelectChange("fenceHeight", "1.8")}
                        >
                          1.8m (Tall)
                        </Badge>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <Label>Fence Color</Label>
                      <div className="flex flex-wrap gap-3">
                        <Badge
                          className={`px-4 py-2 cursor-pointer ${formData.fenceColor === "natural" ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                          onClick={() => handleSelectChange("fenceColor", "natural")}
                        >
                          Natural Timber
                        </Badge>
                        <Badge
                          className={`px-4 py-2 cursor-pointer ${formData.fenceColor === "stained" ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                          onClick={() => handleSelectChange("fenceColor", "stained")}
                        >
                          Stained
                        </Badge>
                        <Badge
                          className={`px-4 py-2 cursor-pointer ${formData.fenceColor === "painted" ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                          onClick={() => handleSelectChange("fenceColor", "painted")}
                        >
                          Painted
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Gate Options - Show if project type is gate or both */}
              {(formData.projectType === "gate" || formData.projectType === "both") && (
                <div className="space-y-6 border-t pt-6">
                  <h3 className="text-lg font-medium">Gate Options</h3>

                  <div className="space-y-4">
                    <Label>Gate Type</Label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${formData.gateType === "country-swing" ? "border-[#5d8c62] bg-[#5d8c62]/5 ring-1 ring-[#5d8c62]" : "hover:border-gray-300"}`}
                        onClick={() => handleSelectChange("gateType", "country-swing")}
                      >
                        <div className="h-24 bg-gray-100 rounded-md mb-3 flex items-center justify-center">
                          <Image
                            src="/country-swing-timber-gate.png"
                            alt="Country Swing Gate"
                            width={150}
                            height={100}
                            className="rounded-md"
                          />
                        </div>
                        <h4 className="font-medium">Country Swing Gate</h4>
                        <p className="text-sm text-gray-500 mt-1">Classic rural style</p>
                      </div>

                      <div
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${formData.gateType === "decorative" ? "border-[#5d8c62] bg-[#5d8c62]/5 ring-1 ring-[#5d8c62]" : "hover:border-gray-300"}`}
                        onClick={() => handleSelectChange("gateType", "decorative")}
                      >
                        <div className="h-24 bg-gray-100 rounded-md mb-3 flex items-center justify-center">
                          <Image
                            src="/decorative-timber-gate.png"
                            alt="Decorative Gate"
                            width={150}
                            height={100}
                            className="rounded-md"
                          />
                        </div>
                        <h4 className="font-medium">Decorative Gate</h4>
                        <p className="text-sm text-gray-500 mt-1">Stylish and elegant</p>
                      </div>

                      <div
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${formData.gateType === "security" ? "border-[#5d8c62] bg-[#5d8c62]/5 ring-1 ring-[#5d8c62]" : "hover:border-gray-300"}`}
                        onClick={() => handleSelectChange("gateType", "security")}
                      >
                        <div className="h-24 bg-gray-100 rounded-md mb-3 flex items-center justify-center">
                          <Image
                            src="/solid-timber-security-gate.png"
                            alt="Security Gate"
                            width={150}
                            height={100}
                            className="rounded-md"
                          />
                        </div>
                        <h4 className="font-medium">Solid Security Gate</h4>
                        <p className="text-sm text-gray-500 mt-1">Maximum privacy and security</p>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <Label>Gate Width</Label>
                      <div className="flex flex-wrap gap-3">
                        <Badge
                          className={`px-4 py-2 cursor-pointer ${formData.gateWidth === 3.6 ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                          onClick={() => handleSelectChange("gateWidth", "3.6")}
                        >
                          3.6m (Standard)
                        </Badge>
                        <Badge
                          className={`px-4 py-2 cursor-pointer ${formData.gateWidth === 4.2 ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                          onClick={() => handleSelectChange("gateWidth", "4.2")}
                        >
                          4.2m (Wide)
                        </Badge>
                        <Badge
                          className={`px-4 py-2 cursor-pointer ${formData.gateWidth === 4.8 ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                          onClick={() => handleSelectChange("gateWidth", "4.8")}
                        >
                          4.8m (Extra Wide)
                        </Badge>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <Label>Gate Automation</Label>
                      <div className="flex flex-wrap gap-3">
                        <Badge
                          className={`px-4 py-2 cursor-pointer ${!formData.automationRequired ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                          onClick={() => handleSwitchChange("automationRequired", false)}
                        >
                          Manual (No Automation)
                        </Badge>
                        <Badge
                          className={`px-4 py-2 cursor-pointer ${formData.automationRequired && formData.automationType === "solar" ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                          onClick={() => {
                            handleSwitchChange("automationRequired", true)
                            handleSelectChange("automationType", "solar")
                          }}
                        >
                          Solar Powered
                        </Badge>
                        <Badge
                          className={`px-4 py-2 cursor-pointer ${formData.automationRequired && formData.automationType === "240v" ? "bg-[#5d8c62] hover:bg-[#4d7352]" : "bg-gray-100 hover:bg-gray-200 text-gray-800"}`}
                          onClick={() => {
                            handleSwitchChange("automationRequired", true)
                            handleSelectChange("automationType", "240v")
                          }}
                        >
                          240V Powered
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Timber Type - Common for both fence and gate */}
              <div className="space-y-4 border-t pt-6">
                <h3 className="text-lg font-medium">Timber Type</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${formData.timberType === "cypress" ? "border-[#5d8c62] bg-[#5d8c62]/5 ring-1 ring-[#5d8c62]" : "hover:border-gray-300"}`}
                    onClick={() => handleSelectChange("timberType", "cypress")}
                  >
                    <div className="h-16 bg-gray-100 rounded-md mb-3 flex items-center justify-center">
                      <Image
                        src="/placeholder-yz15o.png"
                        alt="Cypress"
                        width={100}
                        height={64}
                        className="rounded-md"
                      />
                    </div>
                    <h4 className="font-medium">Cypress</h4>
                    <p className="text-xs text-gray-500 mt-1">Insect & weather resistant</p>
                  </div>

                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${formData.timberType === "treated-pine" ? "border-[#5d8c62] bg-[#5d8c62]/5 ring-1 ring-[#5d8c62]" : "hover:border-gray-300"}`}
                    onClick={() => handleSelectChange("timberType", "treated-pine")}
                  >
                    <div className="h-16 bg-gray-100 rounded-md mb-3 flex items-center justify-center">
                      <Image
                        src="/placeholder-bhq0b.png"
                        alt="Treated Pine"
                        width={100}
                        height={64}
                        className="rounded-md"
                      />
                    </div>
                    <h4 className="font-medium">Treated Pine</h4>
                    <p className="text-xs text-gray-500 mt-1">Economical option</p>
                  </div>

                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${formData.timberType === "hardwood" ? "border-[#5d8c62] bg-[#5d8c62]/5 ring-1 ring-[#5d8c62]" : "hover:border-gray-300"}`}
                    onClick={() => handleSelectChange("timberType", "hardwood")}
                  >
                    <div className="h-16 bg-gray-100 rounded-md mb-3 flex items-center justify-center">
                      <Image
                        src="/placeholder-ybtw3.png"
                        alt="Hardwood"
                        width={100}
                        height={64}
                        className="rounded-md"
                      />
                    </div>
                    <h4 className="font-medium">Hardwood</h4>
                    <p className="text-xs text-gray-500 mt-1">Premium durability</p>
                  </div>

                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${formData.timberType === "cedar" ? "border-[#5d8c62] bg-[#5d8c62]/5 ring-1 ring-[#5d8c62]" : "hover:border-gray-300"}`}
                    onClick={() => handleSelectChange("timberType", "cedar")}
                  >
                    <div className="h-16 bg-gray-100 rounded-md mb-3 flex items-center justify-center">
                      <Image
                        src="/cedar-timber-texture.png"
                        alt="Cedar"
                        width={100}
                        height={64}
                        className="rounded-md"
                      />
                    </div>
                    <h4 className="font-medium">Cedar</h4>
                    <p className="text-xs text-gray-500 mt-1">Beautiful natural look</p>
                  </div>
                </div>
              </div>

              {/* Additional Notes */}
              <div className="space-y-4 border-t pt-6">
                <Label htmlFor="additionalNotes">Additional Notes</Label>
                <Textarea
                  id="additionalNotes"
                  name="additionalNotes"
                  placeholder="Any special requirements or questions?"
                  value={formData.additionalNotes}
                  onChange={handleInputChange}
                  rows={4}
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={prevStep}>
                Previous Step
              </Button>
              <Button onClick={nextStep} className="bg-[#5d8c62] hover:bg-[#4d7352]">
                Next Step
              </Button>
            </CardFooter>
          </Card>
        )}

        {/* Step 3: Quote Result */}
        {step === 3 && (
          <div className="space-y-8">
            {!quoteGenerated ? (
              <Card>
                <CardHeader>
                  <CardTitle>Generate Your Quote</CardTitle>
                  <CardDescription>Review your information and generate your personalized quote.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h3 className="font-medium">Your Details</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-500">Name:</span>
                          <span>{formData.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Email:</span>
                          <span>{formData.email}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Phone:</span>
                          <span>{formData.phone}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Property Address:</span>
                          <span>{formData.propertyAddress || formData.address}</span>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <h3 className="font-medium">Project Specifications</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-500">Project Type:</span>
                          <span>
                            {formData.projectType === "both"
                              ? "Fence and Gate"
                              : formData.projectType === "fence"
                                ? "Fence Only"
                                : "Gate Only"}
                          </span>
                        </div>
                        {(formData.projectType === "fence" || formData.projectType === "both") && (
                          <>
                            <div className="flex justify-between">
                              <span className="text-gray-500">Fence Type:</span>
                              <span>{formData.fenceType.replace(/-/g, " ")}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-500">Fence Length:</span>
                              <span>{calculatedMeasurements.totalFenceLength.toFixed(1)} meters</span>
                            </div>
                          </>
                        )}
                        {(formData.projectType === "gate" || formData.projectType === "both") && (
                          <>
                            <div className="flex justify-between">
                              <span className="text-gray-500">Gate Type:</span>
                              <span>{formData.gateType.replace(/-/g, " ")}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-500">Gate Count:</span>
                              <span>{calculatedMeasurements.gateCount || 1}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-500">Automation:</span>
                              <span>{formData.automationRequired ? formData.automationType : "None"}</span>
                            </div>
                          </>
                        )}
                        <div className="flex justify-between">
                          <span className="text-gray-500">Timber Type:</span>
                          <span>{formData.timberType.replace(/-/g, " ")}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium mb-2">Estimated Price</h3>
                    <div className="text-3xl font-bold text-[#5d8c62]">
                      ${Math.round(realtimePrice.estimatedTotal).toLocaleString()}
                    </div>
                    <p className="text-sm text-gray-500 mt-2">
                      This is an approximate quote based on your selections. Lisa will review and provide a final quote.
                    </p>
                  </div>

                  {formData.additionalNotes && (
                    <div className="space-y-2">
                      <h3 className="font-medium">Additional Notes</h3>
                      <p className="text-sm text-gray-600">{formData.additionalNotes}</p>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={prevStep}>
                    Previous Step
                  </Button>
                  <Button onClick={generateQuote} className="bg-[#5d8c62] hover:bg-[#4d7352]" disabled={loading}>
                    {loading ? (
                      <span className="flex items-center">
                        <svg
                          className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Generating Quote...
                      </span>
                    ) : (
                      "Generate Quote"
                    )}
                  </Button>
                </CardFooter>
              </Card>
            ) : (
              <div className="space-y-8">
                <Card>
                  <CardHeader className="bg-[#5d8c62] text-white">
                    <CardTitle>Your Personalized Quote</CardTitle>
                    <CardDescription className="text-white/80">Based on the information you provided</CardDescription>
                  </CardHeader>
                  <CardContent className="pt-6 space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div>
                        <h3 className="text-lg font-medium mb-4">Quote Overview</h3>
                        <div className="bg-gray-50 p-6 rounded-lg">
                          <div className="flex justify-between items-center mb-4 pb-4 border-b">
                            <span className="text-gray-600">Materials:</span>
                            <span className="font-medium">${quoteResult.materials.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center mb-4 pb-4 border-b">
                            <span className="text-gray-600">Labor:</span>
                            <span className="font-medium">${quoteResult.labor.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center mb-4 pb-4 border-b">
                            <span className="text-gray-600">Delivery:</span>
                            <span className="font-medium">${quoteResult.delivery.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center mb-4 pb-4 border-b">
                            <span className="text-gray-600">Subtotal:</span>
                            <span className="font-medium">${quoteResult.subtotal.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center mb-4 pb-4 border-b">
                            <span className="text-gray-600">GST (10%):</span>
                            <span className="font-medium">${quoteResult.gst.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center text-lg">
                            <span className="font-medium">Total:</span>
                            <span className="font-bold text-[#5d8c62]">${quoteResult.total.toLocaleString()}</span>
                          </div>
                        </div>

                        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-100 rounded-lg">
                          <div className="flex">
                            <div className="flex-shrink-0">
                              <svg
                                className="h-5 w-5 text-yellow-400"
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            </div>
                            <div className="ml-3">
                              <h3 className="text-sm font-medium text-yellow-800">Important Note</h3>
                              <div className="mt-2 text-sm text-yellow-700">
                                <p>This quote is valid for 30 days and subject to a final site inspection.</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-lg font-medium mb-4">Project Details</h3>
                        <div className="space-y-4">
                          <div className="flex justify-between pb-2 border-b">
                            <span className="text-gray-600">Project Type:</span>
                            <span className="font-medium capitalize">
                              {formData.projectType === "both"
                                ? "Fence and Gate"
                                : formData.projectType === "fence"
                                  ? "Fence Only"
                                  : "Gate Only"}
                            </span>
                          </div>
                          {(formData.projectType === "fence" || formData.projectType === "both") && (
                            <>
                              <div className="flex justify-between pb-2 border-b">
                                <span className="text-gray-600">Fence Type:</span>
                                <span className="font-medium capitalize">{formData.fenceType.replace(/-/g, " ")}</span>
                              </div>
                              <div className="flex justify-between pb-2 border-b">
                                <span className="text-gray-600">Fence Length:</span>
                                <span className="font-medium">
                                  {calculatedMeasurements.totalFenceLength.toFixed(1)} meters
                                </span>
                              </div>
                            </>
                          )}
                          {(formData.projectType === "gate" || formData.projectType === "both") && (
                            <>
                              <div className="flex justify-between pb-2 border-b">
                                <span className="text-gray-600">Gate Type:</span>
                                <span className="font-medium capitalize">{formData.gateType.replace(/-/g, " ")}</span>
                              </div>
                              <div className="flex justify-between pb-2 border-b">
                                <span className="text-gray-600">Gate Count:</span>
                                <span className="font-medium">{calculatedMeasurements.gateCount || 1}</span>
                              </div>
                              {formData.automationRequired && (
                                <div className="flex justify-between pb-2 border-b">
                                  <span className="text-gray-600">Automation:</span>
                                  <span className="font-medium capitalize">{formData.automationType}</span>
                                </div>
                              )}
                            </>
                          )}
                          <div className="flex justify-between pb-2 border-b">
                            <span className="text-gray-600">Timber Type:</span>
                            <span className="font-medium capitalize">{formData.timberType.replace(/-/g, " ")}</span>
                          </div>
                          <div className="flex justify-between pb-2 border-b">
                            <span className="text-gray-600">Estimated Installation:</span>
                            <span className="font-medium">{quoteResult.estimatedDays} days</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg border">
                      <h3 className="text-lg font-medium mb-4">Message from Our Team</h3>
                      {aiThinking ? (
                        <div className="flex items-center space-x-2 text-gray-500">
                          <svg
                            className="animate-spin h-5 w-5"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          <span>Our AI is preparing a personalized message...</span>
                        </div>
                      ) : (
                        <div className="prose prose-sm max-w-none">
                          {aiResponse.split("\n").map((line, i) => (
                            <p key={i}>{line}</p>
                          ))}
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex flex-wrap gap-4">
                    <Button className="bg-[#5d8c62] hover:bg-[#4d7352]">Accept Quote & Book Consultation</Button>
                    <Button variant="outline">Download PDF</Button>
                    <Button variant="outline">Request Modifications</Button>
                  </CardFooter>
                </Card>

                <div className="text-center">
                  <p className="text-sm text-gray-500 mb-4">Need to make changes or have questions?</p>
                  <div className="flex flex-wrap justify-center gap-4">
                    <Button variant="outline" onClick={() => setQuoteGenerated(false)}>
                      Edit Quote Details
                    </Button>
                    <Link href="/#contact">
                      <Button variant="outline">Contact Our Team</Button>
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </main>

      {/* Floating Voice Assistant */}
      <FloatingVoiceAssistant
        onTranscriptionComplete={handleFormVoiceInput}
        onConfirm={nextStep}
        requiredFields={getRequiredFieldsForStep()}
      />
    </div>
  )
}
