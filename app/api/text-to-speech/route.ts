import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { text } = body

    if (!text) {
      return NextResponse.json({ error: "No text provided" }, { status: 400 })
    }

    // Since we don't have a real TTS service, we'll return a success response
    // The client will use the Web Speech API for actual speech synthesis
    return NextResponse.json({
      success: true,
      message: "Text to speech conversion successful",
      text: text,
      shouldUseClientTTS: true,
    })
  } catch (error) {
    console.error("Error in text-to-speech:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unknown error occurred" },
      { status: 500 },
    )
  }
}
