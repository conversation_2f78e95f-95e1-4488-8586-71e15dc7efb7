import { type NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { text } = body

    if (!text) {
      return NextResponse.json({ error: "No text provided" }, { status: 400 })
    }

    // No server-side TTS service configured, instruct client to use Web Speech API
    return NextResponse.json({
      success: true,
      message: "No server-side TTS configured. Use client-side Web Speech API.",
      text: text,
      shouldUseClientTTS: true,
    })
  } catch (error) {
    console.error("Error in text-to-speech:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unknown error occurred" },
      { status: 500 },
    )
  }
}
