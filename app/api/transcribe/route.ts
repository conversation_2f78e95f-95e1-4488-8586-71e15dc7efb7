import { type NextRequest, NextResponse } from "next/server"
import { groq } from "@ai-sdk/groq"
import { generateText } from "ai"

// Add ElevenLabs API client
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY
const ELEVENLABS_API_URL = "https://api.elevenlabs.io/v1/speech-to-text"

export async function POST(request: NextRequest) {
  try {
    // Check if the request is multipart/form-data
    if (!request.headers.get("content-type")?.includes("multipart/form-data")) {
      return NextResponse.json({ error: "Request must be multipart/form-data" }, { status: 400 })
    }

    const formData = await request.formData()
    const audioFile = formData.get("audio") as File | null
    const fieldName = formData.get("fieldName") as string | null

    if (!audioFile) {
      return NextResponse.json({ error: "No audio file provided" }, { status: 400 })
    }

    if (!fieldName) {
      return NextResponse.json({ error: "No field name provided" }, { status: 400 })
    }

    // Convert the audio file to a buffer
    const audioBuffer = await audioFile.arrayBuffer()
    const audioBlob = new Blob([audioBuffer], { type: audioFile.type })

    // Create a system prompt based on the field name
    let systemPrompt = "You are a helpful assistant that transcribes audio for a fencing quote form. "
    
    switch (fieldName) {
      case "name":
        systemPrompt += "The user is saying their full name. Format it properly with capitalization."
        break
      case "email":
        systemPrompt += "The user is saying their email address. Format it as a valid email without spaces."
        break
      case "phone":
        systemPrompt += "The user is saying their phone number. Format it as a valid Australian phone number."
        break
      case "address":
        systemPrompt += "The user is saying their address. Format it properly with capitalization."
        break
      case "propertyAddress":
        systemPrompt += "The user is saying a property address. Format it properly with capitalization."
        break
      case "additionalNotes":
        systemPrompt += "The user is providing additional notes or requirements for their fencing project."
        break
      default:
        systemPrompt += "Transcribe the audio accurately."
    }

    // Use ElevenLabs for transcription
    if (ELEVENLABS_API_KEY) {
      try {
        // Create form data for ElevenLabs API
        const elevenLabsFormData = new FormData()
        elevenLabsFormData.append("audio", audioBlob)
        elevenLabsFormData.append("model_id", "whisper-1")

        // Call ElevenLabs API
        const transcriptionResponse = await fetch(ELEVENLABS_API_URL, {
          method: "POST",
          headers: {
            "xi-api-key": ELEVENLABS_API_KEY,
          },
          body: elevenLabsFormData,
        })

        if (!transcriptionResponse.ok) {
          throw new Error(`ElevenLabs API error: ${transcriptionResponse.statusText}`)
        }

        const transcriptionData = await transcriptionResponse.json()
        const transcribedText = transcriptionData.text

        // Use Groq to refine the transcription based on the field context
        const { text } = await generateText({
          model: groq("llama3-70b-8192"),
          prompt: `Please format the following transcribed text appropriately: ${transcribedText}`,
          system: systemPrompt,
        })

        // Return the transcribed and formatted text
        return NextResponse.json({ text })
      } catch (elevenLabsError) {
        console.error("Error using ElevenLabs for transcription:", elevenLabsError)
        // Fall back to Groq if ElevenLabs fails
      }
    }

    // Fallback to Groq if ElevenLabs is not configured
    const { text } = await generateText({
      model: groq("llama3-70b-8192"),
      prompt: `Please transcribe the following audio content. ${fieldName === "email" ? "This is an email address." : ""} ${fieldName === "phone" ? "This is a phone number." : ""}`,
      system: systemPrompt,
    })

    return NextResponse.json({ text })
  } catch (error) {
    console.error("Error in transcription:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unknown error occurred" },
      { status: 500 },
    )
  }
}
