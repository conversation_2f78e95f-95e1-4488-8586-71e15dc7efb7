import { type NextRequest, NextResponse } from "next/server"
import { groq } from "@ai-sdk/groq"
import { generateText } from "ai"

// Add ElevenLabs API client
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY
const ELEVENLABS_API_URL = "https://api.elevenlabs.io/v1/speech-to-text"

export async function POST(request: NextRequest) {
  try {
    // Check if the request is multipart/form-data
    const contentType = request.headers.get("content-type") || ""
    if (!contentType.includes("multipart/form-data")) {
      return NextResponse.json({ error: "Request must be multipart/form-data" }, { status: 400 })
    }

    const formData = await request.formData()
    const audioFile = formData.get("audio") as File | null
    const type = formData.get("type") as string | null

    if (!audioFile) {
      return NextResponse.json({ error: "No audio file provided" }, { status: 400 })
    }

    // Convert the audio file to a buffer
    const audioBuffer = await audioFile.arrayBuffer()
    const audioBlob = new Blob([audioBuffer], { type: audioFile.type })

    // Create a system prompt to help with context
    const systemPrompt = `
      You are a helpful assistant that transcribes audio for a fencing quote form.
      The user is giving voice commands to navigate through the form.

      Identify if the transcription contains any of these navigation commands:
      - "next step", "continue", "go forward", "proceed", "next page" -> COMMAND: next
      - "previous step", "go back", "back", "return" -> COMMAND: previous
      - "go to step 1", "first step", "start", "beginning" -> COMMAND: step1
      - "go to step 2", "second step", "site details" -> COMMAND: step2
      - "go to step 3", "third step", "fence details" -> COMMAND: step3
      - "go to step 4", "fourth step", "quote", "final step" -> COMMAND: step4
      - "submit", "generate quote", "finish", "complete" -> COMMAND: submit
      - "draw fence", "add fence line", "create fence" -> COMMAND: drawFence
      - "add gate", "place gate", "insert gate" -> COMMAND: addGate
      - "confirm measurements", "save measurements" -> COMMAND: confirmMeasurements

      Return the transcribed text and the identified command if present.
    `

    // Use ElevenLabs for transcription
    if (ELEVENLABS_API_KEY) {
      try {
        // Create form data for ElevenLabs API
        const elevenLabsFormData = new FormData()
        elevenLabsFormData.append("audio", audioBlob)
        elevenLabsFormData.append("model_id", "whisper-1")

        // Call ElevenLabs API
        const transcriptionResponse = await fetch(ELEVENLABS_API_URL, {
          method: "POST",
          headers: {
            "xi-api-key": ELEVENLABS_API_KEY,
          },
          body: elevenLabsFormData,
        })

        if (!transcriptionResponse.ok) {
          throw new Error(`ElevenLabs API error: ${transcriptionResponse.statusText}`)
        }

        const transcriptionData = await transcriptionResponse.json()
        const transcribedText = transcriptionData.text

        // Extract command from the transcription
        const command = extractCommand(transcribedText)

        // Return the transcribed text and command
        return NextResponse.json({
          text: transcribedText,
          command,
        })
      } catch (elevenLabsError) {
        console.error("Error using ElevenLabs for transcription:", elevenLabsError)
        // Fall back to Groq if ElevenLabs fails
      }
    }

    // If we have a Groq API key, use it for transcription as fallback
    if (process.env.GROQ_API_KEY) {
      try {
        // Use Groq to transcribe the audio
        const { text } = await generateText({
          model: groq("llama3-70b-8192"),
          prompt: "Please transcribe the following audio content and identify any navigation commands.",
          system: systemPrompt,
        })

        // Extract command from the transcription
        const command = extractCommand(text)

        // Return the transcribed text and command
        return NextResponse.json({
          text,
          command,
        })
      } catch (groqError) {
        console.error("Error using Groq for transcription:", groqError)
        return NextResponse.json(
          { error: "Transcription service unavailable. Please check your API configuration." },
          { status: 503 }
        )
      }
    }

    // Return error if no transcription service is configured
    return NextResponse.json(
      { error: "No transcription service configured. Please set up ElevenLabs or Groq API keys." },
      { status: 503 }
    )
  } catch (error) {
    console.error("Error in command transcription:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unknown error occurred" },
      { status: 500 },
    )
  }
}

// Update the extractCommand function to include map interaction commands
function extractCommand(text: string): string | null {
  if (!text) return null

  const lowerText = text.toLowerCase()

  // Navigation commands
  if (lowerText.includes("next step") || lowerText.includes("continue") ||
      lowerText.includes("go forward") || lowerText.includes("proceed") ||
      lowerText.includes("next page")) {
    return "next"
  }

  // Add map interaction commands
  if (lowerText.includes("draw fence") || lowerText.includes("add fence line") ||
      lowerText.includes("create fence")) {
    return "drawFence"
  }

  if (lowerText.includes("add gate") || lowerText.includes("place gate") ||
      lowerText.includes("insert gate")) {
    return "addGate"
  }

  if (lowerText.includes("confirm measurements") || lowerText.includes("save measurements")) {
    return "confirmMeasurements"
  }

  // Keep existing command extraction logic
  if (
    lowerText.includes("previous step") ||
    lowerText.includes("go back") ||
    lowerText.includes("back") ||
    lowerText.includes("return")
  ) {
    return "previous"
  }

  if (
    lowerText.includes("step 1") ||
    lowerText.includes("first step") ||
    lowerText.includes("start") ||
    lowerText.includes("beginning")
  ) {
    return "step1"
  }

  if (lowerText.includes("step 2") || lowerText.includes("second step") || lowerText.includes("site details")) {
    return "step2"
  }

  if (lowerText.includes("step 3") || lowerText.includes("third step") || lowerText.includes("fence details")) {
    return "step3"
  }

  if (
    lowerText.includes("step 4") ||
    lowerText.includes("fourth step") ||
    lowerText.includes("quote") ||
    lowerText.includes("final step")
  ) {
    return "step4"
  }

  if (
    lowerText.includes("submit") ||
    lowerText.includes("generate quote") ||
    lowerText.includes("finish") ||
    lowerText.includes("complete")
  ) {
    return "submit"
  }

  return null
}
