import { type NextRequest, NextResponse } from "next/server"
import { groq } from "@ai-sdk/groq"
import { generateText } from "ai"

// Add ElevenLabs API client
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY
const ELEVENLABS_API_URL = "https://api.elevenlabs.io/v1/speech-to-text"

export async function POST(request: NextRequest) {
  try {
    // Check if the request is multipart/form-data
    if (!request.headers.get("content-type")?.includes("multipart/form-data")) {
      return NextResponse.json({ error: "Request must be multipart/form-data" }, { status: 400 })
    }

    const formData = await request.formData()
    const audioFile = formData.get("audio") as File | null

    if (!audioFile) {
      return NextResponse.json({ error: "No audio file provided" }, { status: 400 })
    }

    // Convert the audio file to a buffer
    const audioBuffer = await audioFile.arrayBuffer()
    const audioBlob = new Blob([audioBuffer], { type: audioFile.type })

    // Create a system prompt to help with context
    const systemPrompt = `
      You are a helpful assistant that transcribes audio for a fencing quote form. 
      The user is providing information for multiple form fields at once.
      Extract the following information if mentioned:
      - Full name
      - Email address (format as a valid email)
      - Phone number (format as a valid Australian phone number)
      - Address
      - Property address (if different from the main address)
      - Any additional notes or requirements
      
      Return the transcribed text and also extract the specific fields into a structured format.
    `

    // Use ElevenLabs for transcription
    if (ELEVENLABS_API_KEY) {
      try {
        // Create form data for ElevenLabs API
        const elevenLabsFormData = new FormData()
        elevenLabsFormData.append("audio", audioBlob)
        elevenLabsFormData.append("model_id", "whisper-1")

        // Call ElevenLabs API
        const transcriptionResponse = await fetch(ELEVENLABS_API_URL, {
          method: "POST",
          headers: {
            "xi-api-key": ELEVENLABS_API_KEY,
          },
          body: elevenLabsFormData,
        })

        if (!transcriptionResponse.ok) {
          throw new Error(`ElevenLabs API error: ${transcriptionResponse.statusText}`)
        }

        const transcriptionData = await transcriptionResponse.json()
        const transcribedText = transcriptionData.text

        // Use Groq to extract structured information from the transcription
        const { text } = await generateText({
          model: groq("llama3-70b-8192"),
          prompt: `Please analyze this transcribed text and extract form field information: ${transcribedText}`,
          system: systemPrompt,
        })

        // Extract fields from the transcription using the existing function
        const extractedFields = extractFields(transcribedText)

        // Return the transcribed text and extracted fields
        return NextResponse.json({
          text: transcribedText,
          fields: extractedFields,
        })
      } catch (elevenLabsError) {
        console.error("Error using ElevenLabs for transcription:", elevenLabsError)
        // Fall back to Groq if ElevenLabs fails
      }
    }

    // Fallback to Groq if ElevenLabs is not configured
    const { text } = await generateText({
      model: groq("llama3-70b-8192"),
      prompt: "Please transcribe the following audio content and extract form field information.",
      system: systemPrompt,
    })

    // Extract fields from the transcription
    const extractedFields = extractFields(text)

    // Return the transcribed text and extracted fields
    return NextResponse.json({
      text,
      fields: extractedFields,
    })
  } catch (error) {
    console.error("Error in transcription:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unknown error occurred" },
      { status: 500 },
    )
  }
}

// Keep the existing extractFields function
const extractFields = (transcription: string): Record<string, string> => {
  const fields: Record<string, string> = {}

  // Name extraction
  const namePatterns = [
    /my name is ([^,.]+)/i,
    /name is ([^,.]+)/i,
    /i am ([^,.]+)/i,
    /i'm ([^,.]+)/i,
    /([^,.]+) is my name/i,
  ]

  for (const pattern of namePatterns) {
    const match = transcription.match(pattern)
    if (match && match[1]) {
      fields.name = match[1].trim()
      break
    }
  }

  // Email extraction
  const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/
  const emailMatch = transcription.match(emailPattern)
  if (emailMatch) {
    fields.email = emailMatch[0]
  }

  // Phone extraction
  const phonePatterns = [
    /(\d{4}\s?\d{3}\s?\d{3})/, // 0412 345 678
    /(\d{4}\s?\d{6})/, // 0412 345678
    /(\d{10})/, // 0412345678
    /(\d{4}[-\s]?\d{3}[-\s]?\d{3})/, // 0412-345-678
  ]

  for (const pattern of phonePatterns) {
    const match = transcription.match(pattern)
    if (match && match[1]) {
      fields.phone = match[1].replace(/[-\s]/g, " ").trim()
      break
    }
  }

  // Address extraction
  const lowerText = transcription.toLowerCase()
  if (lowerText.includes("address") || lowerText.includes("live at") || lowerText.includes("located at")) {
    const addressPatterns = [
      /(?:my address is|address is|i live at|located at) ([^.]+)/i,
      /(?:address|location)(?:\s+is|:)?\s+([^.]+)/i,
    ]

    for (const pattern of addressPatterns) {
      const match = transcription.match(pattern)
      if (match && match[1]) {
        fields.address = match[1].trim()
        break
      }
    }
  }

  // Property address extraction (if different from address)
  if (lowerText.includes("property") || lowerText.includes("fence location") || lowerText.includes("site")) {
    const propertyPatterns = [
      /(?:property address is|property is at|fence location is|site is at) ([^.]+)/i,
      /(?:property|site|fence location)(?:\s+is|:)?\s+([^.]+)/i,
    ]

    for (const pattern of propertyPatterns) {
      const match = transcription.match(pattern)
      if (match && match[1] && !fields.address?.includes(match[1])) {
        fields.propertyAddress = match[1].trim()
        break
      }
    }
  }

  // Additional notes extraction
  if (lowerText.includes("note") || lowerText.includes("additional") || lowerText.includes("comment")) {
    const notesPatterns = [
      /(?:notes|additional notes|comments)(?:\s+are|:)?\s+([^.]+)/i,
      /(?:please note|note that) ([^.]+)/i,
    ]

    for (const pattern of notesPatterns) {
      const match = transcription.match(pattern)
      if (match && match[1]) {
        fields.additionalNotes = match[1].trim()
        break
      }
    }
  }

  return fields
}
