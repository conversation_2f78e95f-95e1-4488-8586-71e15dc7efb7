import { type NextRequest, NextResponse } from "next/server"
import { groq } from "@ai-sdk/groq"
import { streamText } from "ai"

// Add ElevenLabs API client
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY
const ELEVENLABS_API_URL = "https://api.elevenlabs.io/v1/speech-to-text/stream"

export async function POST(request: NextRequest) {
  try {
    if (!request.headers.get("content-type")?.includes("multipart/form-data")) {
      return NextResponse.json({ error: "Request must be multipart/form-data" }, { status: 400 })
    }

    const formData = await request.formData()
    const audioFile = formData.get("audio") as File | null
    const fieldName = formData.get("fieldName") as string | null

    if (!audioFile) {
      return NextResponse.json({ error: "No audio file provided" }, { status: 400 })
    }

    if (!fieldName) {
      return NextResponse.json({ error: "No field name provided" }, { status: 400 })
    }

    // Convert the audio file to a buffer
    const audioBuffer = await audioFile.arrayBuffer()
    const audioBlob = new Blob([audioBuffer], { type: audioFile.type })
    
    // For real-time transcription with ElevenLabs
    if (ELEVENLABS_API_KEY) {
      try {
        // Create form data for ElevenLabs API
        const elevenLabsFormData = new FormData()
        elevenLabsFormData.append("audio", audioBlob)
        elevenLabsFormData.append("model_id", "whisper-1")
        elevenLabsFormData.append("stream", "true")

        // Call ElevenLabs streaming API
        const transcriptionResponse = await fetch(ELEVENLABS_API_URL, {
          method: "POST",
          headers: {
            "xi-api-key": ELEVENLABS_API_KEY,
          },
          body: elevenLabsFormData,
        })

        if (!transcriptionResponse.ok) {
          throw new Error(`ElevenLabs API error: ${transcriptionResponse.statusText}`)
        }

        // Pass through the streaming response
        return new Response(transcriptionResponse.body, {
          headers: {
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
          },
        })
      } catch (elevenLabsError) {
        console.error("Error using ElevenLabs for streaming transcription:", elevenLabsError)
        // Fall back to simulated streaming
      }
    }

    // Simulate streaming responses for development/testing
    const encoder = new TextEncoder()
    const readable = new ReadableStream({
      start(controller) {
        // Simulate real-time transcription by sending partial results
        const words = [
          "My", "name", "is", "John", "Smith", "and", "my", "email", "is", 
          "<EMAIL>", "My", "phone", "number", "is", "0412", "345", "678"
        ]
        
        let index = 0
        const interval = setInterval(() => {
          if (index < words.length) {
            const data = JSON.stringify({
              type: "partial",
              text: words.slice(0, index + 1).join(" "),
              word: words[index],
              confidence: 0.9,
              timestamp: Date.now()
            })
            controller.enqueue(encoder.encode(`data: ${data}\n\n`))
            index++
          } else {
            // Send final result
            const finalData = JSON.stringify({
              type: "final",
              text: words.join(" "),
              confidence: 0.95,
              timestamp: Date.now()
            })
            controller.enqueue(encoder.encode(`data: ${finalData}\n\n`))
            controller.close()
            clearInterval(interval)
          }
        }, 200) // Send a word every 200ms
      }
    })

    return new Response(readable, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    })
  } catch (error) {
    console.error("Error in streaming transcription:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unknown error occurred" },
      { status: 500 },
    )
  }
}
