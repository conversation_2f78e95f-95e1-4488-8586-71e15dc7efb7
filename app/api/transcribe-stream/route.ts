import { type NextRequest, NextResponse } from "next/server"
import { groq } from "@ai-sdk/groq"
import { streamText } from "ai"

// Add ElevenLabs API client
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY
const ELEVENLABS_API_URL = "https://api.elevenlabs.io/v1/speech-to-text/stream"

export async function POST(request: NextRequest) {
  try {
    if (!request.headers.get("content-type")?.includes("multipart/form-data")) {
      return NextResponse.json({ error: "Request must be multipart/form-data" }, { status: 400 })
    }

    const formData = await request.formData()
    const audioFile = formData.get("audio") as File | null
    const fieldName = formData.get("fieldName") as string | null

    if (!audioFile) {
      return NextResponse.json({ error: "No audio file provided" }, { status: 400 })
    }

    if (!fieldName) {
      return NextResponse.json({ error: "No field name provided" }, { status: 400 })
    }

    // Convert the audio file to a buffer
    const audioBuffer = await audioFile.arrayBuffer()
    const audioBlob = new Blob([audioBuffer], { type: audioFile.type })

    // For real-time transcription with ElevenLabs
    if (ELEVENLABS_API_KEY) {
      try {
        // Create form data for ElevenLabs API
        const elevenLabsFormData = new FormData()
        elevenLabsFormData.append("audio", audioBlob)
        elevenLabsFormData.append("model_id", "whisper-1")
        elevenLabsFormData.append("stream", "true")

        // Call ElevenLabs streaming API
        const transcriptionResponse = await fetch(ELEVENLABS_API_URL, {
          method: "POST",
          headers: {
            "xi-api-key": ELEVENLABS_API_KEY,
          },
          body: elevenLabsFormData,
        })

        if (!transcriptionResponse.ok) {
          throw new Error(`ElevenLabs API error: ${transcriptionResponse.statusText}`)
        }

        // Pass through the streaming response
        return new Response(transcriptionResponse.body, {
          headers: {
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
          },
        })
      } catch (elevenLabsError) {
        console.error("Error using ElevenLabs for streaming transcription:", elevenLabsError)
        // Fall back to simulated streaming
      }
    }

    // Return error if no streaming transcription service is configured
    return NextResponse.json(
      { error: "No streaming transcription service configured. Please set up ElevenLabs API key for real-time transcription." },
      { status: 503 }
    )
  } catch (error) {
    console.error("Error in streaming transcription:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unknown error occurred" },
      { status: 500 },
    )
  }
}
