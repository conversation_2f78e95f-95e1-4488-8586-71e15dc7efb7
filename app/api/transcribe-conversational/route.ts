import { type NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    // Check if the request is multipart/form-data
    if (!request.headers.get("content-type")?.includes("multipart/form-data")) {
      return NextResponse.json({ error: "Request must be multipart/form-data" }, { status: 400 });
    }

    const formData = await request.formData();
    const audioFile = formData.get("audio") as File | null;
    
    // Get conversation context from headers
    const agentId = request.headers.get("X-Agent-ID");
    const currentStep = request.headers.get("X-Current-Step");
    const conversationContext = request.headers.get("X-Conversation-Context");

    if (!audioFile) {
      return NextResponse.json({ error: "No audio file provided" }, { status: 400 });
    }

    if (!agentId) {
      return NextResponse.json({ error: "Agent ID required" }, { status: 400 });
    }

    // Convert the audio file to a buffer
    const audioBuffer = await audioFile.arrayBuffer();
    const audioBlob = new Blob([audioBuffer], { type: audioFile.type });

    let transcribedText = "";
    let agentResponse = {
      message: "I didn't catch that. Could you please repeat?",
      action: "continue",
      field: null,
      value: null,
      next_step: "retry"
    };

    // First, transcribe the audio using ElevenLabs Speech-to-Text
    const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;
    if (ELEVENLABS_API_KEY) {
      try {
        // Create form data for ElevenLabs API
        const elevenLabsFormData = new FormData();
        elevenLabsFormData.append("audio", audioBlob);
        elevenLabsFormData.append("model_id", "whisper-1");

        // Call ElevenLabs Speech-to-Text API
        const transcriptionResponse = await fetch("https://api.elevenlabs.io/v1/speech-to-text", {
          method: "POST",
          headers: {
            "xi-api-key": ELEVENLABS_API_KEY,
          },
          body: elevenLabsFormData,
        });

        if (transcriptionResponse.ok) {
          const transcriptionData = await transcriptionResponse.json();
          transcribedText = transcriptionData.text;
        }
      } catch (error) {
        console.error("ElevenLabs transcription error:", error);
      }
    }

    // If we have transcribed text, process it with the conversational agent
    if (transcribedText) {
      try {
        // Parse conversation context
        let context = [];
        if (conversationContext) {
          try {
            context = JSON.parse(conversationContext);
          } catch (e) {
            console.warn("Failed to parse conversation context");
          }
        }

        // Generate conversational response based on current step and context
        agentResponse = generateConversationalResponse(transcribedText, currentStep, context);

      } catch (error) {
        console.error("Error generating conversational response:", error);
      }
    }

    // Generate audio response using ElevenLabs Text-to-Speech
    let audioResponse = null;
    if (ELEVENLABS_API_KEY && agentResponse.message) {
      try {
        const ttsResponse = await fetch("https://api.elevenlabs.io/v1/text-to-speech/cgSgspJ2msm6clMCkdW9", {
          method: "POST",
          headers: {
            "xi-api-key": ELEVENLABS_API_KEY,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            text: agentResponse.message,
            model_id: "eleven_turbo_v2",
            voice_settings: {
              stability: 0.6,
              similarity_boost: 0.8,
            },
          }),
        });

        if (ttsResponse.ok) {
          const audioBuffer = await ttsResponse.arrayBuffer();
          audioResponse = Buffer.from(audioBuffer).toString('base64');
        }
      } catch (error) {
        console.error("ElevenLabs TTS error:", error);
      }
    }

    return NextResponse.json({
      user_text: transcribedText,
      agent_response: agentResponse,
      audio_response: audioResponse,
      conversation_context: {
        step: currentStep,
        agent_id: agentId,
      },
    });

  } catch (error) {
    console.error("Error in conversational transcription:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unknown error occurred" },
      { status: 500 }
    );
  }
}

// Generate conversational response based on user input and context
function generateConversationalResponse(userText: string, currentStep: string | null, context: any[]) {
  const lowerText = userText.toLowerCase();

  if (currentStep === "contact") {
    // Handle contact information collection
    
    // Check for name
    const namePatterns = [
      /my name is ([^,.]+)/i,
      /name is ([^,.]+)/i,
      /i am ([^,.]+)/i,
      /i'm ([^,.]+)/i,
      /([a-z]+ [a-z]+)/i, // Simple first last name pattern
    ];

    for (const pattern of namePatterns) {
      const match = userText.match(pattern);
      if (match && match[1]) {
        const name = match[1].trim();
        return {
          message: `Nice to meet you, ${name}! What's your email address?`,
          action: "collect_info",
          field: "name",
          value: name,
          next_step: "collect_email"
        };
      }
    }

    // Check for email
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
    const emailMatch = userText.match(emailPattern);
    if (emailMatch) {
      return {
        message: "Perfect! And what's your phone number?",
        action: "collect_info",
        field: "email",
        value: emailMatch[0],
        next_step: "collect_phone"
      };
    }

    // Check for phone
    const phonePatterns = [
      /(\d{4}\s?\d{3}\s?\d{3})/, // 0412 345 678
      /(\d{4}\s?\d{6})/, // 0412 345678
      /(\d{10})/, // 0412345678
    ];

    for (const pattern of phonePatterns) {
      const match = userText.match(pattern);
      if (match && match[1]) {
        const phone = match[1].replace(/[-\s]/g, " ").trim();
        return {
          message: "Great! Now, what's the address where you need the fencing work done?",
          action: "collect_info",
          field: "phone",
          value: phone,
          next_step: "collect_address"
        };
      }
    }

    // Check for address
    if (lowerText.includes("street") || lowerText.includes("road") || lowerText.includes("avenue") || 
        lowerText.includes("drive") || lowerText.includes("vic") || lowerText.includes("nsw") ||
        lowerText.includes("qld") || lowerText.includes("wa") || lowerText.includes("sa") ||
        lowerText.includes("tas") || lowerText.includes("nt") || lowerText.includes("act")) {
      return {
        message: "Excellent! I have all your contact details. Let's move on to designing your fence layout on the map.",
        action: "complete",
        field: "propertyAddress",
        value: userText,
        next_step: "map_design"
      };
    }

    // Default response for contact step
    return {
      message: "I'd like to collect your contact information. Could you tell me your name first?",
      action: "collect_info",
      field: "name",
      value: null,
      next_step: "collect_name"
    };

  } else if (currentStep === "design") {
    // Handle map design guidance
    
    if (lowerText.includes("done") || lowerText.includes("finished") || lowerText.includes("complete")) {
      return {
        message: "Perfect! Your fence design looks great. Let's proceed to the next step to choose your fence options.",
        action: "complete",
        field: "fence_design",
        value: "complete",
        next_step: "fence_options"
      };
    }

    if (lowerText.includes("help") || lowerText.includes("how")) {
      return {
        message: "To draw fence lines, click the 'Draw Fence' button and then click points on the map to create your fence line. Double-click to finish a line. For gates, click 'Place Gate' and then click where you want the gate on the map.",
        action: "guide_design",
        field: "fence_design",
        value: null,
        next_step: "continue_design"
      };
    }

    if (lowerText.includes("gate") || lowerText.includes("entrance")) {
      return {
        message: "For gates, click the 'Place Gate' button and then click on the map where you want your gate. You can place multiple gates if needed.",
        action: "guide_design",
        field: "gate_placement",
        value: null,
        next_step: "continue_design"
      };
    }

    // Default response for design step
    return {
      message: "I'm here to help with your fence design. You can ask me about drawing fence lines, placing gates, or let me know when you're done with your design.",
      action: "guide_design",
      field: "fence_design",
      value: null,
      next_step: "continue_design"
    };
  }

  // Default fallback
  return {
    message: "I'm here to help you with your fencing quote. What would you like to know?",
    action: "continue",
    field: null,
    value: null,
    next_step: "continue"
  };
}
