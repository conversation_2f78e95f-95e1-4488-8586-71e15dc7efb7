import { type NextRequest, NextResponse } from "next/server";

const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;

export async function POST(
  request: NextRequest,
  { params }: { params: { agentId: string } }
) {
  try {
    if (!ELEVENLABS_API_KEY) {
      return NextResponse.json(
        { error: "ElevenLabs API key not configured" },
        { status: 500 }
      );
    }

    const { agentId } = params;
    const body = await request.json();
    const { context } = body;

    // For now, return a mock WebSocket URL since we're using the MCP server
    // In a real implementation, this would create a conversation session
    // and return the actual WebSocket URL from ElevenLabs

    // The actual implementation would look like this:
    // const response = await fetch("https://api.elevenlabs.io/v1/convai/conversation", {
    //   method: "POST",
    //   headers: {
    //     "xi-api-key": ELEVENLABS_API_KEY,
    //     "Content-Type": "application/json",
    //   },
    //   body: JSON.stringify({
    //     agent_id: agentId,
    //     context: context || {},
    //   }),
    // });

    // For development, we'll simulate the connection
    const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const websocketUrl = `wss://api.elevenlabs.io/v1/convai/conversation/${conversationId}`;

    return NextResponse.json({
      conversation_id: conversationId,
      websocket_url: websocketUrl,
      agent_id: agentId,
      status: "simulated", // Remove this in production
    });

  } catch (error) {
    console.error("Error connecting to ElevenLabs agent:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "An unknown error occurred" },
      { status: 500 }
    );
  }
}
