import { type NextRequest, NextResponse } from "next/server";

const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;

export async function GET(request: NextRequest) {
  console.log("🔍 [DEBUG] get-signed-url: Starting request");

  try {
    console.log("🔍 [DEBUG] get-signed-url: Checking API key...");
    if (!ELEVENLABS_API_KEY) {
      console.error("❌ [DEBUG] get-signed-url: ElevenLabs API key not configured");
      return NextResponse.json(
        { error: "ElevenLabs API key not configured" },
        { status: 500 }
      );
    }
    console.log("✅ [DEBUG] get-signed-url: API key found");

    // Get agent ID from query params or use default
    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agent_id') || process.env.NEXT_PUBLIC_AGENT_ID || "agent_01jx9ft7qqfhaskjgrggxr6zcj";
    console.log("🔍 [DEBUG] get-signed-url: Using agent ID:", agentId);

    // Get signed URL from ElevenLabs API
    const apiUrl = `https://api.elevenlabs.io/v1/convai/conversation/get-signed-url?agent_id=${agentId}`;
    console.log("🔍 [DEBUG] get-signed-url: Making request to:", apiUrl);

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "xi-api-key": ELEVENLABS_API_KEY,
      },
    });

    console.log("🔍 [DEBUG] get-signed-url: Response status:", response.status);
    console.log("🔍 [DEBUG] get-signed-url: Response headers:", Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ [DEBUG] get-signed-url: ElevenLabs API error:", {
        status: response.status,
        statusText: response.statusText,
        errorText: errorText,
        url: apiUrl
      });
      return NextResponse.json(
        {
          error: `ElevenLabs API error: ${response.statusText}`,
          details: errorText,
          status: response.status
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log("✅ [DEBUG] get-signed-url: Success! Response data:", data);

    return NextResponse.json({
      signedUrl: data.signed_url,
      agentId: agentId,
      debug: {
        timestamp: new Date().toISOString(),
        apiUrl: apiUrl,
        responseStatus: response.status
      }
    });

  } catch (error) {
    console.error("❌ [DEBUG] get-signed-url: Unexpected error:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "An unknown error occurred",
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
