/**
 * Converts an audio blob to a base64 string
 */
export const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      const base64String = reader.result as string
      // Remove the data URL prefix (e.g., "data:audio/webm;base64,")
      const base64 = base64String.split(",")[1]
      resolve(base64)
    }
    reader.onerror = reject
    reader.readAsDataURL(blob)
  })
}

/**
 * Checks if the browser supports the required APIs for voice recording
 */
export const checkVoiceRecordingSupport = (): boolean => {
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia && window.MediaRecorder)
}

/**
 * Creates a voice prompt for a specific field
 */
export const getFieldPrompt = (fieldName: string): string => {
  switch (fieldName) {
    case "name":
      return "Please say your full name"
    case "email":
      return "Please say your email address"
    case "phone":
      return "Please say your phone number"
    case "address":
      return "Please say your address"
    case "propertyAddress":
      return "Please say the property address for the fence"
    case "additionalNotes":
      return "Please describe any additional requirements or notes"
    default:
      return "Please speak now"
  }
}

/**
 * Creates a streaming transcription connection
 */
export const createStreamingTranscription = (
  audioBlob: Blob,
  fieldName: string,
  onPartialResult: (text: string) => void,
  onFinalResult: (text: string) => void,
  onError: (error: string) => void
): void => {
  const formData = new FormData()
  formData.append("audio", audioBlob, "recording.webm")
  formData.append("fieldName", fieldName)

  fetch("/api/transcribe-stream", {
    method: "POST",
    body: formData,
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error("No reader available")
      }

      const decoder = new TextDecoder()
      
      function readStream(): Promise<void> {
        return reader.read().then(({ done, value }) => {
          if (done) {
            return
          }

          const chunk = decoder.decode(value)
          const lines = chunk.split("\n")
          
          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.slice(6))
                if (data.type === "partial") {
                  onPartialResult(data.text)
                } else if (data.type === "final") {
                  onFinalResult(data.text)
                }
              } catch (e) {
                console.warn("Failed to parse SSE data:", line)
              }
            }
          }

          return readStream()
        })
      }

      return readStream()
    })
    .catch((error) => {
      console.error("Streaming transcription error:", error)
      onError(error.message)
    })
}

/**
 * Creates audio chunks for streaming
 */
export const createAudioChunker = (
  onChunk: (chunk: Blob) => void,
  chunkDuration: number = 1000
) => {
  let mediaRecorder: MediaRecorder | null = null
  let audioChunks: Blob[] = []
  let chunkInterval: NodeJS.Timeout | null = null

  const startChunking = (stream: MediaStream) => {
    mediaRecorder = new MediaRecorder(stream, {
      mimeType: "audio/webm;codecs=opus"
    })
    
    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.push(event.data)
      }
    }

    // Create chunks at regular intervals
    chunkInterval = setInterval(() => {
      if (mediaRecorder && mediaRecorder.state === "recording") {
        mediaRecorder.requestData()
        
        if (audioChunks.length > 0) {
          const chunk = new Blob(audioChunks, { type: "audio/webm" })
          onChunk(chunk)
          audioChunks = []
        }
      }
    }, chunkDuration)

    mediaRecorder.start()
  }

  const stopChunking = () => {
    if (chunkInterval) {
      clearInterval(chunkInterval)
      chunkInterval = null
    }
    
    if (mediaRecorder && mediaRecorder.state === "recording") {
      mediaRecorder.stop()
    }
    
    // Process final chunk
    if (audioChunks.length > 0) {
      const finalChunk = new Blob(audioChunks, { type: "audio/webm" })
      onChunk(finalChunk)
      audioChunks = []
    }
  }

  return { startChunking, stopChunking }
}
