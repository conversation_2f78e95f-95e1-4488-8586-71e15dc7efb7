// Add Google Maps types to the window object
declare global {
  interface Window {
    google: any
  }
}

// Geocoding function to convert address to coordinates
export async function geocodeAddress(address: string, apiKey: string): Promise<{ lat: number; lng: number } | null> {
  try {
    // Preprocess the address for better Australian geocoding
    let processedAddress = address.trim()
    
    // If no country specified, add Australia
    if (!processedAddress.toLowerCase().includes('australia') && !processedAddress.toLowerCase().includes('aus')) {
      processedAddress += ', Australia'
    }
    
    console.log("Geocoding request:", { 
      originalAddress: address,
      processedAddress,
      apiKey: apiKey ? `${apiKey.substring(0, 10)}...` : "missing" 
    })
    
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(processedAddress)}&key=${apiKey}&region=au&components=country:AU`
    console.log("Geocoding URL:", url)
    
    const response = await fetch(url)
    const data = await response.json()
    
    console.log("Geocoding response:", data)

    if (data.status === "OK" && data.results && data.results.length > 0) {
      const location = data.results[0].geometry.location
      console.log("Geocoding success:", location)
      return { lat: location.lat, lng: location.lng }
    } else {
      console.error("Geocoding failed:", {
        status: data.status,
        error_message: data.error_message,
        results_count: data.results?.length || 0,
        processedAddress
      })
      
      // If first attempt fails, try with a simpler address
      if (data.status === "ZERO_RESULTS" && processedAddress !== address) {
        console.log("Retrying with original address...")
        return geocodeAddressSimple(address, apiKey)
      }
      
      return null
    }
  } catch (error) {
    console.error("Error geocoding address:", error)
    return null
  }
}

// Fallback geocoding function with minimal processing
async function geocodeAddressSimple(address: string, apiKey: string): Promise<{ lat: number; lng: number } | null> {
  try {
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${apiKey}`
    const response = await fetch(url)
    const data = await response.json()
    
    console.log("Simple geocoding response:", data)

    if (data.status === "OK" && data.results && data.results.length > 0) {
      const location = data.results[0].geometry.location
      return { lat: location.lat, lng: location.lng }
    }
    
    return null
  } catch (error) {
    console.error("Error in simple geocoding:", error)
    return null
  }
}

// Function to get static map image URL
export function getStaticMapImageUrl(
  center: { lat: number; lng: number },
  zoom: number,
  width: number,
  height: number,
  apiKey: string,
): string {
  return `https://maps.googleapis.com/maps/api/staticmap?center=${center.lat},${center.lng}&zoom=${zoom}&size=${width}x${height}&maptype=satellite&key=${apiKey}`
}

// Global variable to track loading state
let isLoading = false
let loadPromise: Promise<void> | null = null

// Function to load Google Maps API script
export function loadGoogleMapsScript(apiKey: string): Promise<void> {
  // If already loaded, return immediately
  if (window.google && window.google.maps) {
    return Promise.resolve()
  }

  // If currently loading, return the existing promise
  if (isLoading && loadPromise) {
    return loadPromise
  }

  // Check if script already exists in DOM
  const existingScript = document.querySelector(`script[src*="maps.googleapis.com"]`)
  if (existingScript) {
    return new Promise((resolve, reject) => {
      const checkInterval = setInterval(() => {
        if (window.google && window.google.maps) {
          clearInterval(checkInterval)
          resolve()
        }
      }, 100)
      
      // Timeout after 10 seconds
      setTimeout(() => {
        clearInterval(checkInterval)
        reject(new Error("Timeout waiting for Google Maps to load"))
      }, 10000)
    })
  }

  isLoading = true
  loadPromise = new Promise((resolve, reject) => {
    const script = document.createElement("script")
    // Include drawing, geometry, and places libraries
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=drawing,geometry,places`
    script.async = true
    script.defer = true
    script.onload = () => {
      isLoading = false
      resolve()
    }
    script.onerror = () => {
      isLoading = false
      loadPromise = null
      reject(new Error("Failed to load Google Maps API"))
    }
    document.head.appendChild(script)
  })

  return loadPromise
}

// Function to calculate distance between two points in meters
export function calculateDistance(point1: { lat: number; lng: number }, point2: { lat: number; lng: number }): number {
  const R = 6371e3 // Earth's radius in meters
  const φ1 = (point1.lat * Math.PI) / 180
  const φ2 = (point2.lat * Math.PI) / 180
  const Δφ = ((point2.lat - point1.lat) * Math.PI) / 180
  const Δλ = ((point2.lng - point1.lng) * Math.PI) / 180

  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  return R * c
}

// Convert pixel coordinates to geo coordinates
export function pixelToGeo(
  x: number,
  y: number,
  canvasWidth: number,
  canvasHeight: number,
  bounds: {
    north: number
    south: number
    east: number
    west: number
  },
): { lat: number; lng: number } {
  const latRange = bounds.north - bounds.south
  const lngRange = bounds.east - bounds.west

  const lat = bounds.north - (y / canvasHeight) * latRange
  const lng = bounds.west + (x / canvasWidth) * lngRange

  return { lat, lng }
}

// Convert geo coordinates to pixel coordinates
export function geoToPixel(
  lat: number,
  lng: number,
  canvasWidth: number,
  canvasHeight: number,
  bounds: {
    north: number
    south: number
    east: number
    west: number
  },
): { x: number; y: number } {
  const latRange = bounds.north - bounds.south
  const lngRange = bounds.east - bounds.west

  const y = ((bounds.north - lat) / latRange) * canvasHeight
  const x = ((lng - bounds.west) / lngRange) * canvasWidth

  return { x, y }
}
